@import "tailwindcss";

@layer theme {
  :root {
    --color-background-accent-blue-default: var(--color-blue-400);
    --color-background-accent-blue-hovered: var(--color-blue-500);
    --color-background-accent-blue-pressed: var(--color-blue-600);
    --color-background-accent-blue-alpha-default: var(--color-transparent);
    --color-background-accent-blue-alpha-hovered: var(--color-blue-200A);
    --color-background-accent-blue-alpha-pressed: var(--color-blue-300A);
    --color-background-accent-blue-bold-default: var(--color-blue-500);
    --color-background-accent-blue-bold-hovered: var(--color-blue-600);
    --color-background-accent-blue-bold-pressed: var(--color-blue-700);
    --color-background-accent-blue-bolder-default: var(--color-blue-600);
    --color-background-accent-blue-bolder-hovered: var(--color-blue-700);
    --color-background-accent-blue-bolder-pressed: var(--color-blue-800);
    --color-background-accent-blue-subtle-default: var(--color-blue-100);
    --color-background-accent-blue-subtle-hovered: var(--color-blue-200);
    --color-background-accent-blue-subtle-pressed: var(--color-blue-300);
    --color-background-accent-blue-subtlest-default: var(--color-blue-50);
    --color-background-accent-blue-subtlest-hovered: var(--color-blue-200);
    --color-background-accent-blue-subtlest-pressed: var(--color-blue-300);
    --color-background-accent-blue-white-default: var(--color-white);
    --color-background-accent-blue-white-hovered: var(--color-blue-100);
    --color-background-accent-blue-white-pressed: var(--color-blue-200);
    --color-background-accent-cyan-default: var(--color-cyan-400);
    --color-background-accent-cyan-hovered: var(--color-cyan-500);
    --color-background-accent-cyan-pressed: var(--color-cyan-600);
    --color-background-accent-cyan-alpha-default: var(--color-transparent);
    --color-background-accent-cyan-alpha-hovered: var(--color-cyan-200A);
    --color-background-accent-cyan-alpha-pressed: var(--color-cyan-300A);
    --color-background-accent-cyan-bold-default: var(--color-cyan-500);
    --color-background-accent-cyan-bold-hovered: var(--color-cyan-600);
    --color-background-accent-cyan-bold-pressed: var(--color-cyan-700);
    --color-background-accent-cyan-bolder-default: var(--color-cyan-600);
    --color-background-accent-cyan-bolder-hovered: var(--color-cyan-700);
    --color-background-accent-cyan-bolder-pressed: var(--color-cyan-800);
    --color-background-accent-cyan-subtle-default: var(--color-cyan-100);
    --color-background-accent-cyan-subtle-hovered: var(--color-cyan-200);
    --color-background-accent-cyan-subtle-pressed: var(--color-cyan-300);
    --color-background-accent-cyan-subtlest-default: var(--color-cyan-50);
    --color-background-accent-cyan-subtlest-hovered: var(--color-cyan-200);
    --color-background-accent-cyan-subtlest-pressed: var(--color-cyan-300);
    --color-background-accent-cyan-white-default: var(--color-white);
    --color-background-accent-cyan-white-hovered: var(--color-cyan-100);
    --color-background-accent-cyan-white-pressed: var(--color-cyan-200);
    --color-background-accent-emerald-default: var(--color-emerald-400);
    --color-background-accent-emerald-hovered: var(--color-emerald-500);
    --color-background-accent-emerald-pressed: var(--color-emerald-600);
    --color-background-accent-emerald-alpha-default: var(--color-transparent);
    --color-background-accent-emerald-alpha-hovered: var(--color-emerald-200A);
    --color-background-accent-emerald-alpha-pressed: var(--color-emerald-300A);
    --color-background-accent-emerald-bold-default: var(--color-emerald-500);
    --color-background-accent-emerald-bold-hovered: var(--color-emerald-600);
    --color-background-accent-emerald-bold-pressed: var(--color-emerald-700);
    --color-background-accent-emerald-bolder-default: var(--color-emerald-600);
    --color-background-accent-emerald-bolder-hovered: var(--color-emerald-700);
    --color-background-accent-emerald-bolder-pressed: var(--color-emerald-800);
    --color-background-accent-emerald-subtle-default: var(--color-emerald-100);
    --color-background-accent-emerald-subtle-hovered: var(--color-emerald-200);
    --color-background-accent-emerald-subtle-pressed: var(--color-emerald-300);
    --color-background-accent-emerald-subtlest-default: var(--color-emerald-50);
    --color-background-accent-emerald-subtlest-hovered: var(--color-emerald-200);
    --color-background-accent-emerald-subtlest-pressed: var(--color-emerald-300);
    --color-background-accent-emerald-white-default: var(--color-white);
    --color-background-accent-emerald-white-hovered: var(--color-emerald-100);
    --color-background-accent-emerald-white-pressed: var(--color-emerald-200);
    --color-background-accent-fuchsia-default: var(--color-fuchsia-400);
    --color-background-accent-fuchsia-hovered: var(--color-fuchsia-500);
    --color-background-accent-fuchsia-pressed: var(--color-fuchsia-600);
    --color-background-accent-fuchsia-alpha-default: var(--color-transparent);
    --color-background-accent-fuchsia-alpha-hovered: var(--color-fuchsia-200A);
    --color-background-accent-fuchsia-alpha-pressed: var(--color-fuchsia-300A);
    --color-background-accent-fuchsia-bold-default: var(--color-fuchsia-500);
    --color-background-accent-fuchsia-bold-hovered: var(--color-fuchsia-600);
    --color-background-accent-fuchsia-bold-pressed: var(--color-fuchsia-700);
    --color-background-accent-fuchsia-bolder-default: var(--color-fuchsia-600);
    --color-background-accent-fuchsia-bolder-hovered: var(--color-fuchsia-700);
    --color-background-accent-fuchsia-bolder-pressed: var(--color-fuchsia-800);
    --color-background-accent-fuchsia-subtle-default: var(--color-fuchsia-100);
    --color-background-accent-fuchsia-subtle-hovered: var(--color-fuchsia-200);
    --color-background-accent-fuchsia-subtle-pressed: var(--color-fuchsia-300);
    --color-background-accent-fuchsia-subtlest-default: var(--color-fuchsia-50);
    --color-background-accent-fuchsia-subtlest-hovered: var(--color-fuchsia-200);
    --color-background-accent-fuchsia-subtlest-pressed: var(--color-fuchsia-300);
    --color-background-accent-fuchsia-white-default: var(--color-white);
    --color-background-accent-fuchsia-white-hovered: var(--color-fuchsia-100);
    --color-background-accent-fuchsia-white-pressed: var(--color-fuchsia-200);
    --color-background-accent-green-default: var(--color-green-400);
    --color-background-accent-green-hovered: var(--color-green-500);
    --color-background-accent-green-pressed: var(--color-green-600);
    --color-background-accent-green-alpha-default: var(--color-transparent);
    --color-background-accent-green-alpha-hovered: var(--color-green-200A);
    --color-background-accent-green-alpha-pressed: var(--color-green-300A);
    --color-background-accent-green-bold-default: var(--color-green-500);
    --color-background-accent-green-bold-hovered: var(--color-green-600);
    --color-background-accent-green-bold-pressed: var(--color-green-700);
    --color-background-accent-green-bolder-default: var(--color-green-600);
    --color-background-accent-green-bolder-hovered: var(--color-green-700);
    --color-background-accent-green-bolder-pressed: var(--color-green-800);
    --color-background-accent-green-subtle-default: var(--color-green-100);
    --color-background-accent-green-subtle-hovered: var(--color-green-200);
    --color-background-accent-green-subtle-pressed: var(--color-green-300);
    --color-background-accent-green-subtlest-default: var(--color-green-50);
    --color-background-accent-green-subtlest-hovered: var(--color-green-200);
    --color-background-accent-green-subtlest-pressed: var(--color-green-300);
    --color-background-accent-green-white-default: var(--color-white);
    --color-background-accent-green-white-hovered: var(--color-green-100);
    --color-background-accent-green-white-pressed: var(--color-green-200);
    --color-background-accent-indigo-default: var(--color-indigo-400);
    --color-background-accent-indigo-hovered: var(--color-indigo-500);
    --color-background-accent-indigo-pressed: var(--color-indigo-600);
    --color-background-accent-indigo-alpha-default: var(--color-transparent);
    --color-background-accent-indigo-alpha-hovered: var(--color-indigo-200A);
    --color-background-accent-indigo-alpha-pressed: var(--color-indigo-300A);
    --color-background-accent-indigo-bold-default: var(--color-indigo-500);
    --color-background-accent-indigo-bold-hovered: var(--color-indigo-600);
    --color-background-accent-indigo-bold-pressed: var(--color-indigo-700);
    --color-background-accent-indigo-bolder-default: var(--color-indigo-600);
    --color-background-accent-indigo-bolder-hovered: var(--color-indigo-700);
    --color-background-accent-indigo-bolder-pressed: var(--color-indigo-800);
    --color-background-accent-indigo-subtle-default: var(--color-indigo-100);
    --color-background-accent-indigo-subtle-hovered: var(--color-indigo-200);
    --color-background-accent-indigo-subtle-pressed: var(--color-indigo-300);
    --color-background-accent-indigo-subtlest-default: var(--color-indigo-50);
    --color-background-accent-indigo-subtlest-hovered: var(--color-indigo-200);
    --color-background-accent-indigo-subtlest-pressed: var(--color-indigo-300);
    --color-background-accent-indigo-white-default: var(--color-white);
    --color-background-accent-indigo-white-hovered: var(--color-indigo-100);
    --color-background-accent-indigo-white-pressed: var(--color-indigo-200);
    --color-background-accent-lime-default: var(--color-lime-400);
    --color-background-accent-lime-hovered: var(--color-lime-500);
    --color-background-accent-lime-pressed: var(--color-lime-600);
    --color-background-accent-lime-alpha-default: var(--color-transparent);
    --color-background-accent-lime-alpha-hovered: var(--color-lime-200A);
    --color-background-accent-lime-alpha-pressed: var(--color-lime-300A);
    --color-background-accent-lime-bold-default: var(--color-lime-500);
    --color-background-accent-lime-bold-hovered: var(--color-lime-600);
    --color-background-accent-lime-bold-pressed: var(--color-lime-700);
    --color-background-accent-lime-bolder-default: var(--color-lime-600);
    --color-background-accent-lime-bolder-hovered: var(--color-lime-700);
    --color-background-accent-lime-bolder-pressed: var(--color-lime-800);
    --color-background-accent-lime-subtle-default: var(--color-lime-100);
    --color-background-accent-lime-subtle-hovered: var(--color-lime-200);
    --color-background-accent-lime-subtle-pressed: var(--color-lime-300);
    --color-background-accent-lime-subtlest-default: var(--color-lime-50);
    --color-background-accent-lime-subtlest-hovered: var(--color-lime-200);
    --color-background-accent-lime-subtlest-pressed: var(--color-lime-300);
    --color-background-accent-lime-white-default: var(--color-white);
    --color-background-accent-lime-white-hovered: var(--color-lime-100);
    --color-background-accent-lime-white-pressed: var(--color-lime-200);
    --color-background-accent-orange-default: var(--color-orange-400);
    --color-background-accent-orange-hovered: var(--color-orange-500);
    --color-background-accent-orange-pressed: var(--color-orange-600);
    --color-background-accent-orange-alpha-default: var(--color-transparent);
    --color-background-accent-orange-alpha-hovered: var(--color-orange-200A);
    --color-background-accent-orange-alpha-pressed: var(--color-orange-300A);
    --color-background-accent-orange-bold-default: var(--color-orange-500);
    --color-background-accent-orange-bold-hovered: var(--color-orange-600);
    --color-background-accent-orange-bold-pressed: var(--color-orange-700);
    --color-background-accent-orange-bolder-default: var(--color-orange-600);
    --color-background-accent-orange-bolder-hovered: var(--color-orange-700);
    --color-background-accent-orange-bolder-pressed: var(--color-orange-800);
    --color-background-accent-orange-subtle-default: var(--color-orange-100);
    --color-background-accent-orange-subtle-hovered: var(--color-orange-200);
    --color-background-accent-orange-subtle-pressed: var(--color-orange-300);
    --color-background-accent-orange-subtlest-default: var(--color-orange-50);
    --color-background-accent-orange-subtlest-hovered: var(--color-orange-200);
    --color-background-accent-orange-subtlest-pressed: var(--color-orange-300);
    --color-background-accent-orange-white-default: var(--color-white);
    --color-background-accent-orange-white-hovered: var(--color-orange-100);
    --color-background-accent-orange-white-pressed: var(--color-orange-200);
    --color-background-accent-pink-default: var(--color-pink-400);
    --color-background-accent-pink-hovered: var(--color-pink-500);
    --color-background-accent-pink-pressed: var(--color-pink-600);
    --color-background-accent-pink-alpha-default: var(--color-transparent);
    --color-background-accent-pink-alpha-hovered: var(--color-pink-200A);
    --color-background-accent-pink-alpha-pressed: var(--color-pink-300A);
    --color-background-accent-pink-bold-default: var(--color-pink-500);
    --color-background-accent-pink-bold-hovered: var(--color-pink-600);
    --color-background-accent-pink-bold-pressed: var(--color-pink-700);
    --color-background-accent-pink-bolder-default: var(--color-pink-600);
    --color-background-accent-pink-bolder-hovered: var(--color-pink-700);
    --color-background-accent-pink-bolder-pressed: var(--color-pink-800);
    --color-background-accent-pink-subtle-default: var(--color-pink-100);
    --color-background-accent-pink-subtle-hovered: var(--color-pink-200);
    --color-background-accent-pink-subtle-pressed: var(--color-pink-300);
    --color-background-accent-pink-subtlest-default: var(--color-pink-50);
    --color-background-accent-pink-subtlest-hovered: var(--color-pink-200);
    --color-background-accent-pink-subtlest-pressed: var(--color-pink-300);
    --color-background-accent-pink-white-default: var(--color-white);
    --color-background-accent-pink-white-hovered: var(--color-pink-100);
    --color-background-accent-pink-white-pressed: var(--color-pink-200);
    --color-background-accent-purple-default: var(--color-purple-400);
    --color-background-accent-purple-hovered: var(--color-purple-500);
    --color-background-accent-purple-pressed: var(--color-purple-600);
    --color-background-accent-purple-alpha-default: var(--color-transparent);
    --color-background-accent-purple-alpha-hovered: var(--color-purple-200A);
    --color-background-accent-purple-alpha-pressed: var(--color-purple-300A);
    --color-background-accent-purple-bold-default: var(--color-purple-500);
    --color-background-accent-purple-bold-hovered: var(--color-purple-600);
    --color-background-accent-purple-bold-pressed: var(--color-purple-700);
    --color-background-accent-purple-bolder-default: var(--color-purple-700);
    --color-background-accent-purple-bolder-hovered: var(--color-purple-800);
    --color-background-accent-purple-bolder-pressed: var(--color-purple-900);
    --color-background-accent-purple-subtle-default: var(--color-purple-100);
    --color-background-accent-purple-subtle-hovered: var(--color-purple-200);
    --color-background-accent-purple-subtle-pressed: var(--color-purple-300);
    --color-background-accent-purple-subtlest-default: var(--color-purple-50);
    --color-background-accent-purple-subtlest-hovered: var(--color-purple-200);
    --color-background-accent-purple-subtlest-pressed: var(--color-purple-300);
    --color-background-accent-purple-white-default: var(--color-white);
    --color-background-accent-purple-white-hovered: var(--color-purple-100);
    --color-background-accent-purple-white-pressed: var(--color-purple-200);
    --color-background-accent-red-default: var(--color-red-400);
    --color-background-accent-red-hovered: var(--color-red-500);
    --color-background-accent-red-pressed: var(--color-red-600);
    --color-background-accent-red-alpha-default: var(--color-transparent);
    --color-background-accent-red-alpha-hovered: var(--color-red-200A);
    --color-background-accent-red-alpha-pressed: var(--color-red-300A);
    --color-background-accent-red-bold-default: var(--color-red-500);
    --color-background-accent-red-bold-hovered: var(--color-red-600);
    --color-background-accent-red-bold-pressed: var(--color-red-700);
    --color-background-accent-red-bolder-default: var(--color-red-700);
    --color-background-accent-red-bolder-hovered: var(--color-red-800);
    --color-background-accent-red-bolder-pressed: var(--color-red-900);
    --color-background-accent-red-subtle-default: var(--color-red-100);
    --color-background-accent-red-subtle-hovered: var(--color-red-200);
    --color-background-accent-red-subtle-pressed: var(--color-red-300);
    --color-background-accent-red-subtlest-default: var(--color-red-50);
    --color-background-accent-red-subtlest-hovered: var(--color-red-200);
    --color-background-accent-red-subtlest-pressed: var(--color-red-300);
    --color-background-accent-red-white-default: var(--color-white);
    --color-background-accent-red-white-hovered: var(--color-red-100);
    --color-background-accent-red-white-pressed: var(--color-red-200);
    --color-background-accent-rose-default: var(--color-rose-400);
    --color-background-accent-rose-hovered: var(--color-rose-500);
    --color-background-accent-rose-pressed: var(--color-rose-600);
    --color-background-accent-rose-alpha-default: var(--color-transparent);
    --color-background-accent-rose-alpha-hovered: var(--color-rose-200A);
    --color-background-accent-rose-alpha-pressed: var(--color-rose-300A);
    --color-background-accent-rose-bold-default: var(--color-rose-500);
    --color-background-accent-rose-bold-hovered: var(--color-rose-600);
    --color-background-accent-rose-bold-pressed: var(--color-rose-700);
    --color-background-accent-rose-bolder-default: var(--color-rose-600);
    --color-background-accent-rose-bolder-hovered: var(--color-rose-700);
    --color-background-accent-rose-bolder-pressed: var(--color-rose-800);
    --color-background-accent-rose-subtle-default: var(--color-rose-100);
    --color-background-accent-rose-subtle-hovered: var(--color-rose-200);
    --color-background-accent-rose-subtle-pressed: var(--color-rose-300);
    --color-background-accent-rose-subtlest-default: var(--color-rose-50);
    --color-background-accent-rose-subtlest-hovered: var(--color-rose-200);
    --color-background-accent-rose-subtlest-pressed: var(--color-rose-300);
    --color-background-accent-rose-white-default: var(--color-white);
    --color-background-accent-rose-white-hovered: var(--color-rose-100);
    --color-background-accent-rose-white-pressed: var(--color-rose-200);
    --color-background-accent-rosedust-default: var(--color-rosedust-400);
    --color-background-accent-rosedust-hovered: var(--color-rosedust-500);
    --color-background-accent-rosedust-pressed: var(--color-rosedust-600);
    --color-background-accent-rosedust-alpha-default: var(--color-transparent);
    --color-background-accent-rosedust-alpha-hovered: var(--color-rosedust-200A);
    --color-background-accent-rosedust-alpha-pressed: var(--color-rosedust-300A);
    --color-background-accent-rosedust-bold-default: var(--color-rosedust-500);
    --color-background-accent-rosedust-bold-hovered: var(--color-rosedust-600);
    --color-background-accent-rosedust-bold-pressed: var(--color-rosedust-700);
    --color-background-accent-rosedust-bolder-default: var(--color-rosedust-600);
    --color-background-accent-rosedust-bolder-hovered: var(--color-rosedust-700);
    --color-background-accent-rosedust-bolder-pressed: var(--color-rosedust-800);
    --color-background-accent-rosedust-subtle-default: var(--color-rosedust-100);
    --color-background-accent-rosedust-subtle-hovered: var(--color-rosedust-200);
    --color-background-accent-rosedust-subtle-pressed: var(--color-rosedust-300);
    --color-background-accent-rosedust-subtlest-default: var(--color-rosedust-50);
    --color-background-accent-rosedust-subtlest-hovered: var(--color-rosedust-200);
    --color-background-accent-rosedust-subtlest-pressed: var(--color-rosedust-300);
    --color-background-accent-rosedust-white-default: var(--color-white);
    --color-background-accent-rosedust-white-hovered: var(--color-rosedust-100);
    --color-background-accent-rosedust-white-pressed: var(--color-rosedust-200);
    --color-background-accent-sky-default: var(--color-sky-400);
    --color-background-accent-sky-hovered: var(--color-sky-500);
    --color-background-accent-sky-pressed: var(--color-sky-600);
    --color-background-accent-sky-alpha-default: var(--color-transparent);
    --color-background-accent-sky-alpha-hovered: var(--color-sky-200A);
    --color-background-accent-sky-alpha-pressed: var(--color-sky-300A);
    --color-background-accent-sky-bold-default: var(--color-sky-500);
    --color-background-accent-sky-bold-hovered: var(--color-sky-600);
    --color-background-accent-sky-bold-pressed: var(--color-sky-700);
    --color-background-accent-sky-bolder-default: var(--color-sky-600);
    --color-background-accent-sky-bolder-hovered: var(--color-sky-700);
    --color-background-accent-sky-bolder-pressed: var(--color-sky-800);
    --color-background-accent-sky-subtle-default: var(--color-sky-100);
    --color-background-accent-sky-subtle-hovered: var(--color-sky-200);
    --color-background-accent-sky-subtle-pressed: var(--color-sky-300);
    --color-background-accent-sky-subtlest-default: var(--color-sky-50);
    --color-background-accent-sky-subtlest-hovered: var(--color-sky-200);
    --color-background-accent-sky-subtlest-pressed: var(--color-sky-300);
    --color-background-accent-sky-white-default: var(--color-white);
    --color-background-accent-sky-white-hovered: var(--color-sky-100);
    --color-background-accent-sky-white-pressed: var(--color-sky-200);
    --color-background-accent-teal-default: var(--color-teal-400);
    --color-background-accent-teal-hovered: var(--color-teal-500);
    --color-background-accent-teal-pressed: var(--color-teal-600);
    --color-background-accent-teal-alpha-default: var(--color-transparent);
    --color-background-accent-teal-alpha-hovered: var(--color-teal-200A);
    --color-background-accent-teal-alpha-pressed: var(--color-teal-300A);
    --color-background-accent-teal-bold-default: var(--color-teal-500);
    --color-background-accent-teal-bold-hovered: var(--color-teal-600);
    --color-background-accent-teal-bold-pressed: var(--color-teal-700);
    --color-background-accent-teal-bolder-default: var(--color-teal-600);
    --color-background-accent-teal-bolder-hovered: var(--color-teal-700);
    --color-background-accent-teal-bolder-pressed: var(--color-teal-800);
    --color-background-accent-teal-subtle-default: var(--color-teal-100);
    --color-background-accent-teal-subtle-hovered: var(--color-teal-200);
    --color-background-accent-teal-subtle-pressed: var(--color-teal-300);
    --color-background-accent-teal-subtlest-default: var(--color-teal-50);
    --color-background-accent-teal-subtlest-hovered: var(--color-teal-200);
    --color-background-accent-teal-subtlest-pressed: var(--color-teal-300);
    --color-background-accent-teal-white-default: var(--color-white);
    --color-background-accent-teal-white-hovered: var(--color-teal-100);
    --color-background-accent-teal-white-pressed: var(--color-teal-200);
    --color-background-accent-yellow-default: var(--color-yellow-400);
    --color-background-accent-yellow-hovered: var(--color-yellow-500);
    --color-background-accent-yellow-pressed: var(--color-yellow-600);
    --color-background-accent-yellow-alpha-default: var(--color-transparent);
    --color-background-accent-yellow-alpha-hovered: var(--color-yellow-200A);
    --color-background-accent-yellow-alpha-pressed: var(--color-yellow-300A);
    --color-background-accent-yellow-bold-default: var(--color-yellow-500);
    --color-background-accent-yellow-bold-hovered: var(--color-yellow-600);
    --color-background-accent-yellow-bold-pressed: var(--color-yellow-700);
    --color-background-accent-yellow-bolder-default: var(--color-yellow-600);
    --color-background-accent-yellow-bolder-hovered: var(--color-yellow-700);
    --color-background-accent-yellow-bolder-pressed: var(--color-yellow-800);
    --color-background-accent-yellow-subtle-default: var(--color-yellow-100);
    --color-background-accent-yellow-subtle-hovered: var(--color-yellow-200);
    --color-background-accent-yellow-subtle-pressed: var(--color-yellow-300);
    --color-background-accent-yellow-subtlest-default: var(--color-yellow-50);
    --color-background-accent-yellow-subtlest-hovered: var(--color-yellow-200);
    --color-background-accent-yellow-subtlest-pressed: var(--color-yellow-300);
    --color-background-accent-yellow-white-default: var(--color-white);
    --color-background-accent-yellow-white-hovered: var(--color-yellow-100);
    --color-background-accent-yellow-white-pressed: var(--color-yellow-200);
    --color-background-brand-default: var(--color-indigo-400);
    --color-background-brand-hovered: var(--color-indigo-500);
    --color-background-brand-pressed: var(--color-indigo-600);
    --color-background-brand-alpha-bold-default: var(--color-indigo-400A);
    --color-background-brand-alpha-bold-hovered: var(--color-indigo-500A);
    --color-background-brand-alpha-bold-pressed: var(--color-indigo-600);
    --color-background-brand-alpha-subtle-default: var(--color-transparent);
    --color-background-brand-alpha-subtle-hovered: var(--color-indigo-200A);
    --color-background-brand-alpha-subtle-pressed: var(--color-indigo-300A);
    --color-background-brand-bold-default: var(--color-indigo-500);
    --color-background-brand-bold-hovered: var(--color-indigo-600);
    --color-background-brand-bold-pressed: var(--color-indigo-700);
    --color-background-brand-bolder-default: var(--color-indigo-600);
    --color-background-brand-bolder-hovered: var(--color-indigo-700);
    --color-background-brand-bolder-pressed: var(--color-indigo-800);
    --color-background-brand-subtle-default: var(--color-indigo-100);
    --color-background-brand-subtle-hovered: var(--color-indigo-200);
    --color-background-brand-subtle-pressed: var(--color-indigo-300);
    --color-background-brand-subtlest-default: var(--color-indigo-50);
    --color-background-brand-subtlest-hovered: var(--color-indigo-200);
    --color-background-brand-subtlest-pressed: var(--color-indigo-300);
    --color-background-brand-white-default: var(--color-white);
    --color-background-brand-white-hovered: var(--color-indigo-100);
    --color-background-brand-white-pressed: var(--color-indigo-200);
    --color-background-danger-default: var(--color-red-400);
    --color-background-danger-hovered: var(--color-red-500);
    --color-background-danger-pressed: var(--color-red-600);
    --color-background-danger-alpha-bold-default: var(--color-red-400A);
    --color-background-danger-alpha-bold-hovered: var(--color-red-500A);
    --color-background-danger-alpha-bold-pressed: var(--color-red-600);
    --color-background-danger-alpha-subtle-default: var(--color-transparent);
    --color-background-danger-alpha-subtle-hovered: var(--color-red-200A);
    --color-background-danger-alpha-subtle-pressed: var(--color-red-300A);
    --color-background-danger-bold-default: var(--color-red-500);
    --color-background-danger-bold-hovered: var(--color-red-600);
    --color-background-danger-bold-pressed: var(--color-red-700);
    --color-background-danger-bolder-default: var(--color-red-700);
    --color-background-danger-bolder-hovered: var(--color-red-800);
    --color-background-danger-bolder-pressed: var(--color-red-900);
    --color-background-danger-subtle-default: var(--color-red-100);
    --color-background-danger-subtle-hovered: var(--color-red-200);
    --color-background-danger-subtle-pressed: var(--color-red-300);
    --color-background-danger-subtlest-default: var(--color-red-50);
    --color-background-danger-subtlest-hovered: var(--color-red-200);
    --color-background-danger-subtlest-pressed: var(--color-red-300);
    --color-background-danger-white-default: var(--color-white);
    --color-background-danger-white-hovered: var(--color-red-100);
    --color-background-danger-white-pressed: var(--color-red-200);
    --color-background-discovery-default: var(--color-purple-400);
    --color-background-discovery-hovered: var(--color-purple-500);
    --color-background-discovery-pressed: var(--color-purple-600);
    --color-background-discovery-alpha-bold-default: var(--color-purple-400A);
    --color-background-discovery-alpha-bold-hovered: var(--color-purple-500A);
    --color-background-discovery-alpha-bold-pressed: var(--color-purple-600);
    --color-background-discovery-alpha-subtle-default: var(--color-transparent);
    --color-background-discovery-alpha-subtle-hovered: var(--color-purple-200A);
    --color-background-discovery-alpha-subtle-pressed: var(--color-purple-300A);
    --color-background-discovery-bold-default: var(--color-purple-500);
    --color-background-discovery-bold-hovered: var(--color-purple-600);
    --color-background-discovery-bold-pressed: var(--color-purple-700);
    --color-background-discovery-bolder-default: var(--color-purple-700);
    --color-background-discovery-bolder-hovered: var(--color-purple-800);
    --color-background-discovery-bolder-pressed: var(--color-purple-900);
    --color-background-discovery-subtle-default: var(--color-purple-100);
    --color-background-discovery-subtle-hovered: var(--color-purple-200);
    --color-background-discovery-subtle-pressed: var(--color-purple-300);
    --color-background-discovery-subtlest-default: var(--color-purple-50);
    --color-background-discovery-subtlest-hovered: var(--color-purple-200);
    --color-background-discovery-subtlest-pressed: var(--color-purple-300);
    --color-background-discovery-white-default: var(--color-white);
    --color-background-discovery-white-hovered: var(--color-purple-100);
    --color-background-discovery-white-pressed: var(--color-purple-200);
    --color-background-info-default: var(--color-blue-400);
    --color-background-info-hovered: var(--color-blue-500);
    --color-background-info-pressed: var(--color-blue-600);
    --color-background-info-alpha-bold-default: var(--color-blue-400A);
    --color-background-info-alpha-bold-hovered: var(--color-blue-500A);
    --color-background-info-alpha-bold-pressed: var(--color-blue-600);
    --color-background-info-alpha-subtle-default: var(--color-transparent);
    --color-background-info-alpha-subtle-hovered: var(--color-blue-200A);
    --color-background-info-alpha-subtle-pressed: var(--color-blue-300A);
    --color-background-info-bold-default: var(--color-blue-500);
    --color-background-info-bold-hovered: var(--color-blue-600);
    --color-background-info-bold-pressed: var(--color-blue-700);
    --color-background-info-bolder-default: var(--color-blue-600);
    --color-background-info-bolder-hovered: var(--color-blue-700);
    --color-background-info-bolder-pressed: var(--color-blue-800);
    --color-background-info-subtle-default: var(--color-blue-100);
    --color-background-info-subtle-hovered: var(--color-blue-200);
    --color-background-info-subtle-pressed: var(--color-blue-300);
    --color-background-info-subtlest-default: var(--color-blue-50);
    --color-background-info-subtlest-hovered: var(--color-blue-200);
    --color-background-info-subtlest-pressed: var(--color-blue-300);
    --color-background-info-white-default: var(--color-white);
    --color-background-info-white-hovered: var(--color-blue-100);
    --color-background-info-white-pressed: var(--color-blue-200);
    --color-background-input-default: var(--color-white);
    --color-background-input-disabled: var(--color-gray-50);
    --color-background-input-hovered: var(--color-gray-50);
    --color-background-input-pressed: var(--color-white);
    --color-background-input-danger-default: var(--color-red-500);
    --color-background-input-danger-hovered: var(--color-red-600);
    --color-background-input-danger-pressed: var(--color-red-700);
    --color-background-input-selected-default: var(--color-indigo-500);
    --color-background-input-selected-hovered: var(--color-indigo-600);
    --color-background-input-selected-pressed: var(--color-indigo-700);
    --color-background-neutral-default: var(--color-gray-400);
    --color-background-neutral-hovered: var(--color-gray-500);
    --color-background-neutral-pressed: var(--color-gray-600);
    --color-background-neutral-alpha-bold-default: var(--color-gray-400A);
    --color-background-neutral-alpha-bold-hovered: var(--color-gray-500A);
    --color-background-neutral-alpha-bold-pressed: var(--color-gray-600);
    --color-background-neutral-alpha-bold-inverted-default: var(--color-white-400A);
    --color-background-neutral-alpha-bold-inverted-hovered: var(--color-white-500A);
    --color-background-neutral-alpha-bold-inverted-pressed: var(--color-white);
    --color-background-neutral-alpha-default-default: var(--color-gray-200A);
    --color-background-neutral-alpha-default-hovered: var(--color-gray-300A);
    --color-background-neutral-alpha-default-pressed: var(--color-gray-400A);
    --color-background-neutral-alpha-default-inverted-default: var(--color-white-300A);
    --color-background-neutral-alpha-default-inverted-hovered: var(--color-white-400A);
    --color-background-neutral-alpha-default-inverted-pressed: var(--color-white-500A);
    --color-background-neutral-alpha-subltest-default: var(--color-transparent);
    --color-background-neutral-alpha-subltest-hovered: var(--color-gray-100A);
    --color-background-neutral-alpha-subltest-pressed: var(--color-gray-200A);
    --color-background-neutral-alpha-subltest-inverted-default: var(--color-transparent);
    --color-background-neutral-alpha-subltest-inverted-hovered: var(--color-white-100A);
    --color-background-neutral-alpha-subltest-inverted-pressed: var(--color-white-200A);
    --color-background-neutral-alpha-subtle-default: var(--color-gray-100A);
    --color-background-neutral-alpha-subtle-hovered: var(--color-gray-200A);
    --color-background-neutral-alpha-subtle-pressed: var(--color-gray-300A);
    --color-background-neutral-alpha-subtle-inverted-default: var(--color-white-100A);
    --color-background-neutral-alpha-subtle-inverted-hovered: var(--color-white-200A);
    --color-background-neutral-alpha-subtle-inverted-pressed: var(--color-white-300A);
    --color-background-neutral-bold-default: var(--color-gray-500);
    --color-background-neutral-bold-hovered: var(--color-gray-600);
    --color-background-neutral-bold-pressed: var(--color-gray-700);
    --color-background-neutral-bolder-default: var(--color-gray-700);
    --color-background-neutral-bolder-hovered: var(--color-gray-800);
    --color-background-neutral-bolder-pressed: var(--color-gray-900);
    --color-background-neutral-subtle-default: var(--color-gray-100);
    --color-background-neutral-subtle-hovered: var(--color-gray-200);
    --color-background-neutral-subtle-pressed: var(--color-gray-300);
    --color-background-neutral-subtlest-default: var(--color-gray-50);
    --color-background-neutral-subtlest-hovered: var(--color-gray-100);
    --color-background-neutral-subtlest-pressed: var(--color-gray-200);
    --color-background-neutral-white-default: var(--color-white);
    --color-background-neutral-white-hovered: var(--color-gray-100);
    --color-background-neutral-white-pressed: var(--color-gray-200);
    --color-background-selected-default: var(--color-indigo-100);
    --color-background-selected-hovered: var(--color-indigo-200);
    --color-background-selected-pressed: var(--color-indigo-300);
    --color-background-selected-alpha-default: var(--color-transparent);
    --color-background-selected-alpha-hovered: var(--color-indigo-200A);
    --color-background-selected-alpha-pressed: var(--color-indigo-300A);
    --color-background-selected-bold-default: var(--color-indigo-500);
    --color-background-selected-bold-hovered: var(--color-indigo-600);
    --color-background-selected-bold-pressed: var(--color-indigo-700);
    --color-background-selected-bolder-default: var(--color-indigo-600);
    --color-background-selected-bolder-hovered: var(--color-indigo-700);
    --color-background-selected-bolder-pressed: var(--color-indigo-800);
    --color-background-selected-subtle-default: var(--color-indigo-50);
    --color-background-selected-subtle-hovered: var(--color-indigo-100);
    --color-background-selected-subtle-pressed: var(--color-indigo-200);
    --color-background-selected-white-default: var(--color-white);
    --color-background-selected-white-hovered: var(--color-indigo-100);
    --color-background-selected-white-pressed: var(--color-indigo-200);
    --color-background-success-default: var(--color-emerald-400);
    --color-background-success-hovered: var(--color-emerald-500);
    --color-background-success-pressed: var(--color-emerald-600);
    --color-background-success-alpha-bold-default: var(--color-emerald-400A);
    --color-background-success-alpha-bold-hovered: var(--color-emerald-500A);
    --color-background-success-alpha-bold-pressed: var(--color-emerald-600);
    --color-background-success-alpha-subtle-default: var(--color-transparent);
    --color-background-success-alpha-subtle-hovered: var(--color-emerald-200A);
    --color-background-success-alpha-subtle-pressed: var(--color-emerald-300A);
    --color-background-success-bold-default: var(--color-emerald-500);
    --color-background-success-bold-hovered: var(--color-emerald-600);
    --color-background-success-bold-pressed: var(--color-emerald-700);
    --color-background-success-bolder-default: var(--color-emerald-600);
    --color-background-success-bolder-hovered: var(--color-emerald-700);
    --color-background-success-bolder-pressed: var(--color-emerald-800);
    --color-background-success-subtle-default: var(--color-emerald-100);
    --color-background-success-subtle-hovered: var(--color-emerald-200);
    --color-background-success-subtle-pressed: var(--color-emerald-300);
    --color-background-success-subtlest-default: var(--color-emerald-50);
    --color-background-success-subtlest-hovered: var(--color-emerald-200);
    --color-background-success-subtlest-pressed: var(--color-emerald-300);
    --color-background-success-white-default: var(--color-white);
    --color-background-success-white-hovered: var(--color-emerald-100);
    --color-background-success-white-pressed: var(--color-emerald-200);
    --color-background-warning-default: var(--color-yellow-400);
    --color-background-warning-hovered: var(--color-yellow-500);
    --color-background-warning-pressed: var(--color-yellow-600);
    --color-background-warning-alpha-bold-default: var(--color-yellow-400A);
    --color-background-warning-alpha-bold-hovered: var(--color-yellow-500A);
    --color-background-warning-alpha-bold-pressed: var(--color-yellow-600);
    --color-background-warning-alpha-subtle-default: var(--color-transparent);
    --color-background-warning-alpha-subtle-hovered: var(--color-yellow-200A);
    --color-background-warning-alpha-subtle-pressed: var(--color-yellow-300A);
    --color-background-warning-bold-default: var(--color-yellow-500);
    --color-background-warning-bold-hovered: var(--color-yellow-600);
    --color-background-warning-bold-pressed: var(--color-yellow-700);
    --color-background-warning-bolder-default: var(--color-yellow-600);
    --color-background-warning-bolder-hovered: var(--color-yellow-700);
    --color-background-warning-bolder-pressed: var(--color-yellow-800);
    --color-background-warning-subtle-default: var(--color-yellow-100);
    --color-background-warning-subtle-hovered: var(--color-yellow-200);
    --color-background-warning-subtle-pressed: var(--color-yellow-300);
    --color-background-warning-subtlest-default: var(--color-yellow-50);
    --color-background-warning-subtlest-hovered: var(--color-yellow-200);
    --color-background-warning-subtlest-pressed: var(--color-yellow-300);
    --color-background-warning-white-default: var(--color-white);
    --color-background-warning-white-hovered: var(--color-yellow-100);
    --color-background-warning-white-pressed: var(--color-yellow-200);
    --color-border-accent-blue-bold: var(--color-blue-600);
    --color-border-accent-blue-default: var(--color-blue-400);
    --color-border-accent-blue-subtle: var(--color-blue-300);
    --color-border-accent-blue-subtlest: var(--color-blue-200);
    --color-border-accent-cyan-bold: var(--color-cyan-600);
    --color-border-accent-cyan-default: var(--color-cyan-400);
    --color-border-accent-cyan-subtle: var(--color-cyan-300);
    --color-border-accent-cyan-subtlest: var(--color-cyan-200);
    --color-border-accent-emerald-bold: var(--color-emerald-600);
    --color-border-accent-emerald-default: var(--color-emerald-400);
    --color-border-accent-emerald-subtle: var(--color-emerald-300);
    --color-border-accent-emerald-subtlest: var(--color-emerald-200);
    --color-border-accent-fuchsia-bold: var(--color-fuchsia-600);
    --color-border-accent-fuchsia-default: var(--color-fuchsia-400);
    --color-border-accent-fuchsia-subtle: var(--color-fuchsia-300);
    --color-border-accent-fuchsia-subtlest: var(--color-fuchsia-200);
    --color-border-accent-green-bold: var(--color-green-600);
    --color-border-accent-green-default: var(--color-green-400);
    --color-border-accent-green-subtle: var(--color-green-300);
    --color-border-accent-green-subtlest: var(--color-green-200);
    --color-border-accent-indigo-bold: var(--color-indigo-600);
    --color-border-accent-indigo-default: var(--color-indigo-400);
    --color-border-accent-indigo-subtle: var(--color-indigo-300);
    --color-border-accent-indigo-subtlest: var(--color-indigo-200);
    --color-border-accent-lime-bold: var(--color-lime-600);
    --color-border-accent-lime-default: var(--color-lime-400);
    --color-border-accent-lime-subtle: var(--color-lime-300);
    --color-border-accent-lime-subtlest: var(--color-lime-200);
    --color-border-accent-orange-bold: var(--color-orange-600);
    --color-border-accent-orange-default: var(--color-orange-400);
    --color-border-accent-orange-subtle: var(--color-orange-300);
    --color-border-accent-orange-subtlest: var(--color-orange-200);
    --color-border-accent-pink-bold: var(--color-pink-600);
    --color-border-accent-pink-default: var(--color-pink-400);
    --color-border-accent-pink-subtle: var(--color-pink-300);
    --color-border-accent-pink-subtlest: var(--color-pink-200);
    --color-border-accent-purple-bold: var(--color-purple-600);
    --color-border-accent-purple-default: var(--color-purple-400);
    --color-border-accent-purple-subtle: var(--color-purple-300);
    --color-border-accent-purple-subtlest: var(--color-purple-200);
    --color-border-accent-red-bold: var(--color-red-600);
    --color-border-accent-red-default: var(--color-red-400);
    --color-border-accent-red-subtle: var(--color-red-300);
    --color-border-accent-red-subtlest: var(--color-red-200);
    --color-border-accent-rose-bold: var(--color-rose-600);
    --color-border-accent-rose-default: var(--color-rose-400);
    --color-border-accent-rose-subtle: var(--color-rose-300);
    --color-border-accent-rose-subtlest: var(--color-rose-200);
    --color-border-accent-rosedust-bold: var(--color-rosedust-600);
    --color-border-accent-rosedust-default: var(--color-rosedust-400);
    --color-border-accent-rosedust-subtle: var(--color-rosedust-300);
    --color-border-accent-rosedust-subtlest: var(--color-rosedust-200);
    --color-border-accent-sky-bold: var(--color-sky-600);
    --color-border-accent-sky-default: var(--color-sky-400);
    --color-border-accent-sky-subtle: var(--color-sky-300);
    --color-border-accent-sky-subtlest: var(--color-sky-200);
    --color-border-accent-teal-bold: var(--color-teal-600);
    --color-border-accent-teal-default: var(--color-teal-400);
    --color-border-accent-teal-subtle: var(--color-teal-300);
    --color-border-accent-teal-subtlest: var(--color-teal-200);
    --color-border-accent-yellow-bold: var(--color-yellow-600);
    --color-border-accent-yellow-default: var(--color-yellow-400);
    --color-border-accent-yellow-subtle: var(--color-yellow-300);
    --color-border-accent-yellow-subtlest: var(--color-yellow-200);
    --color-border-brand-bold: var(--color-indigo-600);
    --color-border-brand-default: var(--color-indigo-400);
    --color-border-brand-subtle: var(--color-indigo-300);
    --color-border-brand-subtlest: var(--color-indigo-200);
    --color-border-danger-bold: var(--color-red-600);
    --color-border-danger-default: var(--color-red-400);
    --color-border-danger-subtle: var(--color-red-300);
    --color-border-danger-subtlest: var(--color-red-200);
    --color-border-discovery-bold: var(--color-purple-600);
    --color-border-discovery-default: var(--color-purple-400);
    --color-border-discovery-subtle: var(--color-purple-300);
    --color-border-discovery-subtlest: var(--color-purple-200);
    --color-border-info-bold: var(--color-blue-600);
    --color-border-info-default: var(--color-blue-400);
    --color-border-info-subtle: var(--color-blue-300);
    --color-border-info-subtlest: var(--color-blue-200);
    --color-border-input-danger: var(--color-red-400);
    --color-border-input-default: var(--color-gray-300A);
    --color-border-input-disabled: var(--color-gray-200A);
    --color-border-input-hovered: var(--color-gray-400A);
    --color-border-input-selected: var(--color-indigo-500);
    --color-border-neutral-default: var(--color-gray-400);
    --color-border-neutral-disabled: var(--color-gray-100);
    --color-border-neutral-hovered: var(--color-gray-500);
    --color-border-neutral-inverse: var(--color-white);
    --color-border-neutral-bold-default: var(--color-gray-500);
    --color-border-neutral-bold-hovered: var(--color-gray-600);
    --color-border-neutral-subtle-default: var(--color-gray-300);
    --color-border-neutral-subtle-hovered: var(--color-gray-400);
    --color-border-neutral-subtlest-default: var(--color-gray-200);
    --color-border-neutral-subtlest-hovered: var(--color-gray-300);
    --color-border-selected-default: var(--color-indigo-500);
    --color-border-selected-hover: var(--color-indigo-600);
    --color-border-selected-subtle-default: var(--color-indigo-300);
    --color-border-selected-subtle-hover: var(--color-indigo-400);
    --color-border-selected-subtlest-default: var(--color-indigo-200);
    --color-border-selected-subtlest-hover: var(--color-indigo-300);
    --color-border-success-bold: var(--color-emerald-600);
    --color-border-success-default: var(--color-emerald-400);
    --color-border-success-subtle: var(--color-emerald-300);
    --color-border-success-subtlest: var(--color-emerald-200);
    --color-border-warning-bold: var(--color-yellow-600);
    --color-border-warning-default: var(--color-yellow-400);
    --color-border-warning-subtle: var(--color-yellow-300);
    --color-border-warning-subtlest: var(--color-yellow-200);
    --color-icon-accent-blue-bold: var(--color-blue-600);
    --color-icon-accent-blue-default: var(--color-blue-500);
    --color-icon-accent-blue-inverse: var(--color-white);
    --color-icon-accent-blue-subtle: var(--color-blue-400);
    --color-icon-accent-blue-subtlest: var(--color-blue-300);
    --color-icon-accent-cyan-bold: var(--color-cyan-600);
    --color-icon-accent-cyan-default: var(--color-cyan-500);
    --color-icon-accent-cyan-inverse: var(--color-white);
    --color-icon-accent-cyan-subtle: var(--color-cyan-400);
    --color-icon-accent-cyan-subtlest: var(--color-cyan-300);
    --color-icon-accent-emerald-bold: var(--color-emerald-600);
    --color-icon-accent-emerald-default: var(--color-emerald-500);
    --color-icon-accent-emerald-inverse: var(--color-white);
    --color-icon-accent-emerald-subtle: var(--color-emerald-400);
    --color-icon-accent-emerald-subtlest: var(--color-emerald-300);
    --color-icon-accent-fuchsia-bold: var(--color-fuchsia-600);
    --color-icon-accent-fuchsia-default: var(--color-fuchsia-500);
    --color-icon-accent-fuchsia-inverse: var(--color-white);
    --color-icon-accent-fuchsia-subtle: var(--color-fuchsia-400);
    --color-icon-accent-fuchsia-subtlest: var(--color-fuchsia-300);
    --color-icon-accent-green-bold: var(--color-green-600);
    --color-icon-accent-green-default: var(--color-green-500);
    --color-icon-accent-green-inverse: var(--color-white);
    --color-icon-accent-green-subtle: var(--color-green-400);
    --color-icon-accent-green-subtlest: var(--color-green-300);
    --color-icon-accent-indigo-bold: var(--color-indigo-600);
    --color-icon-accent-indigo-default: var(--color-indigo-500);
    --color-icon-accent-indigo-inverse: var(--color-white);
    --color-icon-accent-indigo-subtle: var(--color-indigo-400);
    --color-icon-accent-indigo-subtlest: var(--color-indigo-300);
    --color-icon-accent-lime-bold: var(--color-lime-600);
    --color-icon-accent-lime-default: var(--color-lime-500);
    --color-icon-accent-lime-inverse: var(--color-white);
    --color-icon-accent-lime-subtle: var(--color-lime-400);
    --color-icon-accent-lime-subtlest: var(--color-lime-300);
    --color-icon-accent-orange-bold: var(--color-orange-600);
    --color-icon-accent-orange-default: var(--color-orange-500);
    --color-icon-accent-orange-inverse: var(--color-white);
    --color-icon-accent-orange-subtle: var(--color-orange-400);
    --color-icon-accent-orange-subtlest: var(--color-orange-300);
    --color-icon-accent-pink-bold: var(--color-pink-600);
    --color-icon-accent-pink-default: var(--color-pink-500);
    --color-icon-accent-pink-inverse: var(--color-white);
    --color-icon-accent-pink-subtle: var(--color-pink-400);
    --color-icon-accent-pink-subtlest: var(--color-pink-300);
    --color-icon-accent-purple-bold: var(--color-purple-600);
    --color-icon-accent-purple-default: var(--color-purple-500);
    --color-icon-accent-purple-inverse: var(--color-white);
    --color-icon-accent-purple-subtle: var(--color-purple-400);
    --color-icon-accent-purple-subtlest: var(--color-purple-300);
    --color-icon-accent-red-bold: var(--color-red-600);
    --color-icon-accent-red-default: var(--color-red-500);
    --color-icon-accent-red-inverse: var(--color-white);
    --color-icon-accent-red-subtle: var(--color-red-400);
    --color-icon-accent-red-subtlest: var(--color-red-300);
    --color-icon-accent-rose-bold: var(--color-rose-600);
    --color-icon-accent-rose-default: var(--color-rose-500);
    --color-icon-accent-rose-inverse: var(--color-white);
    --color-icon-accent-rose-subtle: var(--color-rose-400);
    --color-icon-accent-rose-subtlest: var(--color-rose-300);
    --color-icon-accent-rosedust-bold: var(--color-rosedust-600);
    --color-icon-accent-rosedust-default: var(--color-rosedust-500);
    --color-icon-accent-rosedust-inverse: var(--color-white);
    --color-icon-accent-rosedust-subtle: var(--color-rosedust-400);
    --color-icon-accent-rosedust-subtlest: var(--color-rosedust-300);
    --color-icon-accent-sky-bold: var(--color-sky-600);
    --color-icon-accent-sky-default: var(--color-sky-500);
    --color-icon-accent-sky-inverse: var(--color-white);
    --color-icon-accent-sky-subtle: var(--color-sky-400);
    --color-icon-accent-sky-subtlest: var(--color-sky-300);
    --color-icon-accent-teal-bold: var(--color-teal-600);
    --color-icon-accent-teal-default: var(--color-teal-500);
    --color-icon-accent-teal-inverse: var(--color-white);
    --color-icon-accent-teal-subtle: var(--color-teal-400);
    --color-icon-accent-teal-subtlest: var(--color-teal-300);
    --color-icon-accent-yellow-bold: var(--color-yellow-600);
    --color-icon-accent-yellow-default: var(--color-yellow-500);
    --color-icon-accent-yellow-inverse: var(--color-white);
    --color-icon-accent-yellow-subtle: var(--color-yellow-400);
    --color-icon-accent-yellow-subtlest: var(--color-yellow-300);
    --color-icon-brand-bold: var(--color-indigo-600);
    --color-icon-brand-default: var(--color-indigo-500);
    --color-icon-brand-inverse: var(--color-white);
    --color-icon-brand-subtle: var(--color-indigo-400);
    --color-icon-brand-subtlest: var(--color-indigo-300);
    --color-icon-danger-bold: var(--color-red-600);
    --color-icon-danger-default: var(--color-red-500);
    --color-icon-danger-inverse: var(--color-white);
    --color-icon-danger-subtle: var(--color-red-400);
    --color-icon-danger-subtlest: var(--color-red-300);
    --color-icon-discovery-bold: var(--color-purple-600);
    --color-icon-discovery-default: var(--color-purple-500);
    --color-icon-discovery-inverse: var(--color-white);
    --color-icon-discovery-subtle: var(--color-purple-400);
    --color-icon-discovery-subtlest: var(--color-purple-300);
    --color-icon-info-bold: var(--color-blue-600);
    --color-icon-info-default: var(--color-blue-500);
    --color-icon-info-inverse: var(--color-white);
    --color-icon-info-subtle: var(--color-blue-400);
    --color-icon-info-subtlest: var(--color-blue-300);
    --color-icon-neutral-bold: var(--color-gray-600);
    --color-icon-neutral-default: var(--color-gray-500);
    --color-icon-neutral-inverse: var(--color-white);
    --color-icon-neutral-subtle: var(--color-gray-400);
    --color-icon-neutral-subtlest: var(--color-gray-300);
    --color-icon-selected-bold: var(--color-indigo-600);
    --color-icon-selected-default: var(--color-indigo-500);
    --color-icon-selected-inverse: var(--color-white);
    --color-icon-selected-subtle: var(--color-indigo-400);
    --color-icon-selected-subtlest: var(--color-indigo-300);
    --color-icon-success-bold: var(--color-emerald-600);
    --color-icon-success-default: var(--color-emerald-500);
    --color-icon-success-inverse: var(--color-white);
    --color-icon-success-subtle: var(--color-emerald-400);
    --color-icon-success-subtlest: var(--color-emerald-300);
    --color-icon-warning-bold: var(--color-yellow-600);
    --color-icon-warning-default: var(--color-yellow-500);
    --color-icon-warning-inverse: var(--color-white);
    --color-icon-warning-subtle: var(--color-yellow-400);
    --color-icon-warning-subtlest: var(--color-yellow-300);
    --color-interaction-hovered: var(--color-gray-200A);
    --color-interaction-pressed: var(--color-gray-300A);
    --color-link-brand-default: var(--color-indigo-600);
    --color-link-brand-hovered: var(--color-indigo-400);
    --color-link-brand-pressed: var(--color-indigo-700);
    --color-link-danger-default: var(--color-red-600);
    --color-link-danger-hovered: var(--color-red-400);
    --color-link-danger-pressed: var(--color-red-700);
    --color-link-discovery-default: var(--color-purple-600);
    --color-link-discovery-hovered: var(--color-purple-400);
    --color-link-discovery-pressed: var(--color-purple-700);
    --color-link-info-default: var(--color-blue-600);
    --color-link-info-hovered: var(--color-blue-400);
    --color-link-info-pressed: var(--color-blue-700);
    --color-link-inverse-default: var(--color-white);
    --color-link-inverse-hovered: var(--color-indigo-300);
    --color-link-inverse-pressed: var(--color-indigo-400);
    --color-link-neutral-default: var(--color-gray-600);
    --color-link-neutral-hovered: var(--color-gray-400);
    --color-link-neutral-pressed: var(--color-gray-700);
    --color-link-success-default: var(--color-emerald-600);
    --color-link-success-hovered: var(--color-emerald-400);
    --color-link-success-pressed: var(--color-emerald-700);
    --color-link-warning-default: var(--color-yellow-600);
    --color-link-warning-hovered: var(--color-yellow-400);
    --color-link-warning-pressed: var(--color-yellow-700);
    --color-overlay-default: var(--color-gray-600A);
    --color-overlay-subtle: var(--color-black-400A);
    --color-overlay-inverse-default: var(--color-white-500A);
    --color-overlay-inverse-subtle: var(--color-white-400A);
    --color-skeleton-default: var(--color-gray-200A);
    --color-skeleton-subtle: var(--color-gray-100A);
    --color-surface-default: var(--color-white);
    --color-surface-hovered: var(--color-gray-100);
    --color-surface-pressed: var(--color-gray-200);
    --color-surface-inverse-default: var(--color-gray-800);
    --color-surface-inverse-hovered: var(--color-gray-700);
    --color-surface-inverse-pressed: var(--color-gray-600);
    --color-surface-navigation-default: var(--color-gray-50);
    --color-surface-navigation-hovered: var(--color-gray-100);
    --color-surface-navigation-pressed: var(--color-gray-200);
    --color-surface-overlay-default: var(--color-white);
    --color-surface-overlay-hovered: var(--color-gray-100);
    --color-surface-overlay-pressed: var(--color-gray-200);
    --color-surface-raised-default: var(--color-white);
    --color-surface-raised-hovered: var(--color-gray-100);
    --color-surface-raised-pressed: var(--color-gray-200);
    --color-surface-sunken-default: var(--color-gray-100);
    --color-text-accent-blue-default: var(--color-blue-700);
    --color-text-accent-blue-inverse: var(--color-white);
    --color-text-accent-blue-subtle: var(--color-blue-500);
    --color-text-accent-cyan-default: var(--color-cyan-700);
    --color-text-accent-cyan-inverse: var(--color-white);
    --color-text-accent-cyan-subtle: var(--color-cyan-500);
    --color-text-accent-emerald-default: var(--color-emerald-700);
    --color-text-accent-emerald-inverse: var(--color-white);
    --color-text-accent-emerald-subtle: var(--color-emerald-500);
    --color-text-accent-fuchsia-default: var(--color-fuchsia-700);
    --color-text-accent-fuchsia-inverse: var(--color-white);
    --color-text-accent-fuchsia-subtle: var(--color-fuchsia-500);
    --color-text-accent-green-default: var(--color-green-700);
    --color-text-accent-green-inverse: var(--color-white);
    --color-text-accent-green-subtle: var(--color-green-500);
    --color-text-accent-indigo-default: var(--color-indigo-700);
    --color-text-accent-indigo-inverse: var(--color-white);
    --color-text-accent-indigo-subtle: var(--color-indigo-500);
    --color-text-accent-lime-default: var(--color-lime-700);
    --color-text-accent-lime-inverse: var(--color-white);
    --color-text-accent-lime-subtle: var(--color-lime-500);
    --color-text-accent-orange-default: var(--color-orange-700);
    --color-text-accent-orange-inverse: var(--color-white);
    --color-text-accent-orange-subtle: var(--color-orange-500);
    --color-text-accent-pink-default: var(--color-pink-700);
    --color-text-accent-pink-inverse: var(--color-white);
    --color-text-accent-pink-subtle: var(--color-pink-500);
    --color-text-accent-purple-default: var(--color-purple-700);
    --color-text-accent-purple-inverse: var(--color-white);
    --color-text-accent-purple-subtle: var(--color-purple-500);
    --color-text-accent-red-default: var(--color-red-700);
    --color-text-accent-red-inverse: var(--color-white);
    --color-text-accent-red-subtle: var(--color-red-500);
    --color-text-accent-rose-default: var(--color-rose-700);
    --color-text-accent-rose-inverse: var(--color-white);
    --color-text-accent-rose-subtle: var(--color-rose-500);
    --color-text-accent-rosedust-default: var(--color-rosedust-700);
    --color-text-accent-rosedust-inverse: var(--color-white);
    --color-text-accent-rosedust-subtle: var(--color-rosedust-500);
    --color-text-accent-sky-default: var(--color-sky-700);
    --color-text-accent-sky-inverse: var(--color-white);
    --color-text-accent-sky-subtle: var(--color-sky-500);
    --color-text-accent-teal-default: var(--color-teal-700);
    --color-text-accent-teal-inverse: var(--color-white);
    --color-text-accent-teal-subtle: var(--color-teal-500);
    --color-text-accent-yellow-default: var(--color-yellow-700);
    --color-text-accent-yellow-inverse: var(--color-white);
    --color-text-accent-yellow-subtle: var(--color-yellow-500);
    --color-text-brand-bold: var(--color-indigo-800);
    --color-text-brand-default: var(--color-indigo-700);
    --color-text-brand-inverse: var(--color-white);
    --color-text-brand-subtle: var(--color-indigo-500);
    --color-text-brand-subtlest: var(--color-indigo-400);
    --color-text-danger-default: var(--color-red-700);
    --color-text-danger-inverse: var(--color-white);
    --color-text-danger-subtle: var(--color-red-500);
    --color-text-discovery-default: var(--color-purple-700);
    --color-text-discovery-inverse: var(--color-white);
    --color-text-discovery-subtle: var(--color-purple-500);
    --color-text-info-default: var(--color-blue-700);
    --color-text-info-inverse: var(--color-white);
    --color-text-info-subtle: var(--color-blue-500);
    --color-text-neutral-bold: var(--color-gray-800);
    --color-text-neutral-default: var(--color-gray-700);
    --color-text-neutral-disabled: var(--color-gray-300);
    --color-text-neutral-inverse: var(--color-white);
    --color-text-neutral-subtle: var(--color-gray-500);
    --color-text-neutral-subtlest: var(--color-gray-400);
    --color-text-selected-bold: var(--color-indigo-800);
    --color-text-selected-default: var(--color-indigo-700);
    --color-text-selected-inverse: var(--color-white);
    --color-text-selected-subtle: var(--color-indigo-500);
    --color-text-selected-subtlest: var(--color-indigo-400);
    --color-text-success-default: var(--color-emerald-700);
    --color-text-success-inverse: var(--color-white);
    --color-text-success-subtle: var(--color-emerald-500);
    --color-text-warning-default: var(--color-yellow-700);
    --color-text-warning-inverse: var(--color-white);
    --color-text-warning-subtle: var(--color-yellow-500);
  }

  [data-theme="dark"] {
    --color-background-accent-blue-default: var(--color-blue-600);
    --color-background-accent-blue-hovered: var(--color-blue-500);
    --color-background-accent-blue-pressed: var(--color-blue-400);
    --color-background-accent-blue-alpha-default: var(--color-transparent);
    --color-background-accent-blue-alpha-hovered: var(--color-blue-200A);
    --color-background-accent-blue-alpha-pressed: var(--color-blue-300A);
    --color-background-accent-blue-bold-default: var(--color-blue-500);
    --color-background-accent-blue-bold-hovered: var(--color-blue-400);
    --color-background-accent-blue-bold-pressed: var(--color-blue-300);
    --color-background-accent-blue-bolder-default: var(--color-blue-500);
    --color-background-accent-blue-bolder-hovered: var(--color-blue-400);
    --color-background-accent-blue-bolder-pressed: var(--color-blue-300);
    --color-background-accent-blue-subtle-default: var(--color-blue-800);
    --color-background-accent-blue-subtle-hovered: var(--color-blue-700);
    --color-background-accent-blue-subtle-pressed: var(--color-blue-600);
    --color-background-accent-blue-subtlest-default: var(--color-blue-900);
    --color-background-accent-blue-subtlest-hovered: var(--color-blue-800);
    --color-background-accent-blue-subtlest-pressed: var(--color-blue-700);
    --color-background-accent-blue-white-default: var(--color-gray-800);
    --color-background-accent-blue-white-hovered: var(--color-blue-800);
    --color-background-accent-blue-white-pressed: var(--color-blue-700);
    --color-background-accent-cyan-default: var(--color-cyan-600);
    --color-background-accent-cyan-hovered: var(--color-cyan-500);
    --color-background-accent-cyan-pressed: var(--color-cyan-400);
    --color-background-accent-cyan-alpha-default: var(--color-transparent);
    --color-background-accent-cyan-alpha-hovered: var(--color-cyan-200A);
    --color-background-accent-cyan-alpha-pressed: var(--color-cyan-300A);
    --color-background-accent-cyan-bold-default: var(--color-cyan-500);
    --color-background-accent-cyan-bold-hovered: var(--color-cyan-400);
    --color-background-accent-cyan-bold-pressed: var(--color-cyan-300);
    --color-background-accent-cyan-bolder-default: var(--color-cyan-500);
    --color-background-accent-cyan-bolder-hovered: var(--color-cyan-400);
    --color-background-accent-cyan-bolder-pressed: var(--color-cyan-300);
    --color-background-accent-cyan-subtle-default: var(--color-cyan-800);
    --color-background-accent-cyan-subtle-hovered: var(--color-cyan-700);
    --color-background-accent-cyan-subtle-pressed: var(--color-cyan-600);
    --color-background-accent-cyan-subtlest-default: var(--color-cyan-900);
    --color-background-accent-cyan-subtlest-hovered: var(--color-cyan-800);
    --color-background-accent-cyan-subtlest-pressed: var(--color-cyan-700);
    --color-background-accent-cyan-white-default: var(--color-gray-800);
    --color-background-accent-cyan-white-hovered: var(--color-cyan-800);
    --color-background-accent-cyan-white-pressed: var(--color-cyan-700);
    --color-background-accent-emerald-default: var(--color-emerald-600);
    --color-background-accent-emerald-hovered: var(--color-emerald-500);
    --color-background-accent-emerald-pressed: var(--color-emerald-400);
    --color-background-accent-emerald-alpha-default: var(--color-transparent);
    --color-background-accent-emerald-alpha-hovered: var(--color-emerald-200A);
    --color-background-accent-emerald-alpha-pressed: var(--color-emerald-300A);
    --color-background-accent-emerald-bold-default: var(--color-emerald-500);
    --color-background-accent-emerald-bold-hovered: var(--color-emerald-400);
    --color-background-accent-emerald-bold-pressed: var(--color-emerald-300);
    --color-background-accent-emerald-bolder-default: var(--color-emerald-500);
    --color-background-accent-emerald-bolder-hovered: var(--color-emerald-400);
    --color-background-accent-emerald-bolder-pressed: var(--color-emerald-300);
    --color-background-accent-emerald-subtle-default: var(--color-emerald-800);
    --color-background-accent-emerald-subtle-hovered: var(--color-emerald-700);
    --color-background-accent-emerald-subtle-pressed: var(--color-emerald-600);
    --color-background-accent-emerald-subtlest-default: var(--color-emerald-900);
    --color-background-accent-emerald-subtlest-hovered: var(--color-emerald-800);
    --color-background-accent-emerald-subtlest-pressed: var(--color-emerald-700);
    --color-background-accent-emerald-white-default: var(--color-gray-800);
    --color-background-accent-emerald-white-hovered: var(--color-emerald-800);
    --color-background-accent-emerald-white-pressed: var(--color-emerald-700);
    --color-background-accent-fuchsia-default: var(--color-fuchsia-600);
    --color-background-accent-fuchsia-hovered: var(--color-fuchsia-500);
    --color-background-accent-fuchsia-pressed: var(--color-fuchsia-400);
    --color-background-accent-fuchsia-alpha-default: var(--color-transparent);
    --color-background-accent-fuchsia-alpha-hovered: var(--color-fuchsia-200A);
    --color-background-accent-fuchsia-alpha-pressed: var(--color-fuchsia-300A);
    --color-background-accent-fuchsia-bold-default: var(--color-fuchsia-500);
    --color-background-accent-fuchsia-bold-hovered: var(--color-fuchsia-400);
    --color-background-accent-fuchsia-bold-pressed: var(--color-fuchsia-300);
    --color-background-accent-fuchsia-bolder-default: var(--color-fuchsia-500);
    --color-background-accent-fuchsia-bolder-hovered: var(--color-fuchsia-400);
    --color-background-accent-fuchsia-bolder-pressed: var(--color-fuchsia-300);
    --color-background-accent-fuchsia-subtle-default: var(--color-fuchsia-800);
    --color-background-accent-fuchsia-subtle-hovered: var(--color-fuchsia-700);
    --color-background-accent-fuchsia-subtle-pressed: var(--color-fuchsia-600);
    --color-background-accent-fuchsia-subtlest-default: var(--color-fuchsia-900);
    --color-background-accent-fuchsia-subtlest-hovered: var(--color-fuchsia-800);
    --color-background-accent-fuchsia-subtlest-pressed: var(--color-fuchsia-700);
    --color-background-accent-fuchsia-white-default: var(--color-gray-800);
    --color-background-accent-fuchsia-white-hovered: var(--color-fuchsia-800);
    --color-background-accent-fuchsia-white-pressed: var(--color-fuchsia-700);
    --color-background-accent-green-default: var(--color-green-600);
    --color-background-accent-green-hovered: var(--color-green-500);
    --color-background-accent-green-pressed: var(--color-green-400);
    --color-background-accent-green-alpha-default: var(--color-transparent);
    --color-background-accent-green-alpha-hovered: var(--color-green-200A);
    --color-background-accent-green-alpha-pressed: var(--color-green-300A);
    --color-background-accent-green-bold-default: var(--color-green-500);
    --color-background-accent-green-bold-hovered: var(--color-green-400);
    --color-background-accent-green-bold-pressed: var(--color-green-300);
    --color-background-accent-green-bolder-default: var(--color-green-500);
    --color-background-accent-green-bolder-hovered: var(--color-green-400);
    --color-background-accent-green-bolder-pressed: var(--color-green-300);
    --color-background-accent-green-subtle-default: var(--color-green-800);
    --color-background-accent-green-subtle-hovered: var(--color-green-700);
    --color-background-accent-green-subtle-pressed: var(--color-green-600);
    --color-background-accent-green-subtlest-default: var(--color-green-900);
    --color-background-accent-green-subtlest-hovered: var(--color-green-800);
    --color-background-accent-green-subtlest-pressed: var(--color-lime-700);
    --color-background-accent-green-white-default: var(--color-gray-800);
    --color-background-accent-green-white-hovered: var(--color-lime-800);
    --color-background-accent-green-white-pressed: var(--color-green-700);
    --color-background-accent-indigo-default: var(--color-indigo-600);
    --color-background-accent-indigo-hovered: var(--color-indigo-500);
    --color-background-accent-indigo-pressed: var(--color-indigo-400);
    --color-background-accent-indigo-alpha-default: var(--color-transparent);
    --color-background-accent-indigo-alpha-hovered: var(--color-indigo-200A);
    --color-background-accent-indigo-alpha-pressed: var(--color-indigo-300A);
    --color-background-accent-indigo-bold-default: var(--color-indigo-500);
    --color-background-accent-indigo-bold-hovered: var(--color-indigo-400);
    --color-background-accent-indigo-bold-pressed: var(--color-indigo-300);
    --color-background-accent-indigo-bolder-default: var(--color-indigo-500);
    --color-background-accent-indigo-bolder-hovered: var(--color-indigo-400);
    --color-background-accent-indigo-bolder-pressed: var(--color-indigo-300);
    --color-background-accent-indigo-subtle-default: var(--color-indigo-800);
    --color-background-accent-indigo-subtle-hovered: var(--color-indigo-700);
    --color-background-accent-indigo-subtle-pressed: var(--color-indigo-600);
    --color-background-accent-indigo-subtlest-default: var(--color-indigo-900);
    --color-background-accent-indigo-subtlest-hovered: var(--color-indigo-800);
    --color-background-accent-indigo-subtlest-pressed: var(--color-indigo-700);
    --color-background-accent-indigo-white-default: var(--color-gray-800);
    --color-background-accent-indigo-white-hovered: var(--color-indigo-800);
    --color-background-accent-indigo-white-pressed: var(--color-indigo-700);
    --color-background-accent-lime-default: var(--color-lime-600);
    --color-background-accent-lime-hovered: var(--color-lime-500);
    --color-background-accent-lime-pressed: var(--color-lime-400);
    --color-background-accent-lime-alpha-default: var(--color-transparent);
    --color-background-accent-lime-alpha-hovered: var(--color-lime-200A);
    --color-background-accent-lime-alpha-pressed: var(--color-lime-300A);
    --color-background-accent-lime-bold-default: var(--color-lime-500);
    --color-background-accent-lime-bold-hovered: var(--color-lime-400);
    --color-background-accent-lime-bold-pressed: var(--color-lime-300);
    --color-background-accent-lime-bolder-default: var(--color-lime-500);
    --color-background-accent-lime-bolder-hovered: var(--color-lime-400);
    --color-background-accent-lime-bolder-pressed: var(--color-lime-300);
    --color-background-accent-lime-subtle-default: var(--color-lime-800);
    --color-background-accent-lime-subtle-hovered: var(--color-lime-700);
    --color-background-accent-lime-subtle-pressed: var(--color-lime-600);
    --color-background-accent-lime-subtlest-default: var(--color-lime-900);
    --color-background-accent-lime-subtlest-hovered: var(--color-lime-800);
    --color-background-accent-lime-subtlest-pressed: var(--color-lime-700);
    --color-background-accent-lime-white-default: var(--color-gray-800);
    --color-background-accent-lime-white-hovered: var(--color-lime-800);
    --color-background-accent-lime-white-pressed: var(--color-lime-700);
    --color-background-accent-orange-default: var(--color-orange-600);
    --color-background-accent-orange-hovered: var(--color-orange-500);
    --color-background-accent-orange-pressed: var(--color-orange-400);
    --color-background-accent-orange-alpha-default: var(--color-transparent);
    --color-background-accent-orange-alpha-hovered: var(--color-orange-200A);
    --color-background-accent-orange-alpha-pressed: var(--color-orange-300A);
    --color-background-accent-orange-bold-default: var(--color-orange-500);
    --color-background-accent-orange-bold-hovered: var(--color-orange-400);
    --color-background-accent-orange-bold-pressed: var(--color-orange-300);
    --color-background-accent-orange-bolder-default: var(--color-orange-500);
    --color-background-accent-orange-bolder-hovered: var(--color-orange-400);
    --color-background-accent-orange-bolder-pressed: var(--color-orange-300);
    --color-background-accent-orange-subtle-default: var(--color-orange-800);
    --color-background-accent-orange-subtle-hovered: var(--color-orange-700);
    --color-background-accent-orange-subtle-pressed: var(--color-orange-600);
    --color-background-accent-orange-subtlest-default: var(--color-orange-900);
    --color-background-accent-orange-subtlest-hovered: var(--color-orange-800);
    --color-background-accent-orange-subtlest-pressed: var(--color-orange-700);
    --color-background-accent-orange-white-default: var(--color-gray-800);
    --color-background-accent-orange-white-hovered: var(--color-orange-800);
    --color-background-accent-orange-white-pressed: var(--color-orange-700);
    --color-background-accent-pink-default: var(--color-pink-600);
    --color-background-accent-pink-hovered: var(--color-pink-500);
    --color-background-accent-pink-pressed: var(--color-pink-400);
    --color-background-accent-pink-alpha-default: var(--color-transparent);
    --color-background-accent-pink-alpha-hovered: var(--color-pink-200A);
    --color-background-accent-pink-alpha-pressed: var(--color-pink-300A);
    --color-background-accent-pink-bold-default: var(--color-pink-500);
    --color-background-accent-pink-bold-hovered: var(--color-pink-400);
    --color-background-accent-pink-bold-pressed: var(--color-pink-300);
    --color-background-accent-pink-bolder-default: var(--color-pink-500);
    --color-background-accent-pink-bolder-hovered: var(--color-pink-400);
    --color-background-accent-pink-bolder-pressed: var(--color-pink-300);
    --color-background-accent-pink-subtle-default: var(--color-pink-800);
    --color-background-accent-pink-subtle-hovered: var(--color-pink-700);
    --color-background-accent-pink-subtle-pressed: var(--color-pink-600);
    --color-background-accent-pink-subtlest-default: var(--color-pink-900);
    --color-background-accent-pink-subtlest-hovered: var(--color-pink-800);
    --color-background-accent-pink-subtlest-pressed: var(--color-pink-700);
    --color-background-accent-pink-white-default: var(--color-gray-800);
    --color-background-accent-pink-white-hovered: var(--color-pink-800);
    --color-background-accent-pink-white-pressed: var(--color-pink-700);
    --color-background-accent-purple-default: var(--color-purple-600);
    --color-background-accent-purple-hovered: var(--color-purple-500);
    --color-background-accent-purple-pressed: var(--color-purple-400);
    --color-background-accent-purple-alpha-default: var(--color-transparent);
    --color-background-accent-purple-alpha-hovered: var(--color-purple-200A);
    --color-background-accent-purple-alpha-pressed: var(--color-purple-300A);
    --color-background-accent-purple-bold-default: var(--color-purple-500);
    --color-background-accent-purple-bold-hovered: var(--color-purple-400);
    --color-background-accent-purple-bold-pressed: var(--color-purple-300);
    --color-background-accent-purple-bolder-default: var(--color-purple-500);
    --color-background-accent-purple-bolder-hovered: var(--color-purple-400);
    --color-background-accent-purple-bolder-pressed: var(--color-purple-300);
    --color-background-accent-purple-subtle-default: var(--color-purple-800);
    --color-background-accent-purple-subtle-hovered: var(--color-purple-700);
    --color-background-accent-purple-subtle-pressed: var(--color-purple-600);
    --color-background-accent-purple-subtlest-default: var(--color-purple-900);
    --color-background-accent-purple-subtlest-hovered: var(--color-purple-800);
    --color-background-accent-purple-subtlest-pressed: var(--color-purple-700);
    --color-background-accent-purple-white-default: var(--color-gray-800);
    --color-background-accent-purple-white-hovered: var(--color-purple-800);
    --color-background-accent-purple-white-pressed: var(--color-purple-700);
    --color-background-accent-red-default: var(--color-red-600);
    --color-background-accent-red-hovered: var(--color-red-500);
    --color-background-accent-red-pressed: var(--color-red-400);
    --color-background-accent-red-alpha-default: var(--color-transparent);
    --color-background-accent-red-alpha-hovered: var(--color-red-200A);
    --color-background-accent-red-alpha-pressed: var(--color-red-300A);
    --color-background-accent-red-bold-default: var(--color-red-500);
    --color-background-accent-red-bold-hovered: var(--color-red-400);
    --color-background-accent-red-bold-pressed: var(--color-red-300);
    --color-background-accent-red-bolder-default: var(--color-red-500);
    --color-background-accent-red-bolder-hovered: var(--color-red-400);
    --color-background-accent-red-bolder-pressed: var(--color-red-300);
    --color-background-accent-red-subtle-default: var(--color-red-800);
    --color-background-accent-red-subtle-hovered: var(--color-red-700);
    --color-background-accent-red-subtle-pressed: var(--color-red-600);
    --color-background-accent-red-subtlest-default: var(--color-red-900);
    --color-background-accent-red-subtlest-hovered: var(--color-red-800);
    --color-background-accent-red-subtlest-pressed: var(--color-red-700);
    --color-background-accent-red-white-default: var(--color-gray-800);
    --color-background-accent-red-white-hovered: var(--color-red-800);
    --color-background-accent-red-white-pressed: var(--color-red-700);
    --color-background-accent-rose-default: var(--color-rose-600);
    --color-background-accent-rose-hovered: var(--color-rose-500);
    --color-background-accent-rose-pressed: var(--color-rose-400);
    --color-background-accent-rose-alpha-default: var(--color-transparent);
    --color-background-accent-rose-alpha-hovered: var(--color-rose-200A);
    --color-background-accent-rose-alpha-pressed: var(--color-rose-300A);
    --color-background-accent-rose-bold-default: var(--color-rose-500);
    --color-background-accent-rose-bold-hovered: var(--color-rose-400);
    --color-background-accent-rose-bold-pressed: var(--color-rose-300);
    --color-background-accent-rose-bolder-default: var(--color-rose-500);
    --color-background-accent-rose-bolder-hovered: var(--color-rose-400);
    --color-background-accent-rose-bolder-pressed: var(--color-rose-300);
    --color-background-accent-rose-subtle-default: var(--color-rose-800);
    --color-background-accent-rose-subtle-hovered: var(--color-rose-700);
    --color-background-accent-rose-subtle-pressed: var(--color-rose-600);
    --color-background-accent-rose-subtlest-default: var(--color-rose-900);
    --color-background-accent-rose-subtlest-hovered: var(--color-rose-800);
    --color-background-accent-rose-subtlest-pressed: var(--color-rose-700);
    --color-background-accent-rose-white-default: var(--color-gray-800);
    --color-background-accent-rose-white-hovered: var(--color-rose-800);
    --color-background-accent-rose-white-pressed: var(--color-rose-700);
    --color-background-accent-rosedust-default: var(--color-rosedust-600);
    --color-background-accent-rosedust-hovered: var(--color-rosedust-500);
    --color-background-accent-rosedust-pressed: var(--color-rosedust-400);
    --color-background-accent-rosedust-alpha-default: var(--color-transparent);
    --color-background-accent-rosedust-alpha-hovered: var(--color-rosedust-200A);
    --color-background-accent-rosedust-alpha-pressed: var(--color-rosedust-300A);
    --color-background-accent-rosedust-bold-default: var(--color-rosedust-500);
    --color-background-accent-rosedust-bold-hovered: var(--color-rosedust-400);
    --color-background-accent-rosedust-bold-pressed: var(--color-rosedust-300);
    --color-background-accent-rosedust-bolder-default: var(--color-rosedust-500);
    --color-background-accent-rosedust-bolder-hovered: var(--color-rosedust-400);
    --color-background-accent-rosedust-bolder-pressed: var(--color-rosedust-300);
    --color-background-accent-rosedust-subtle-default: var(--color-rosedust-800);
    --color-background-accent-rosedust-subtle-hovered: var(--color-rosedust-700);
    --color-background-accent-rosedust-subtle-pressed: var(--color-rosedust-600);
    --color-background-accent-rosedust-subtlest-default: var(--color-rosedust-900);
    --color-background-accent-rosedust-subtlest-hovered: var(--color-rosedust-800);
    --color-background-accent-rosedust-subtlest-pressed: var(--color-rosedust-700);
    --color-background-accent-rosedust-white-default: var(--color-gray-800);
    --color-background-accent-rosedust-white-hovered: var(--color-rosedust-800);
    --color-background-accent-rosedust-white-pressed: var(--color-rosedust-700);
    --color-background-accent-sky-default: var(--color-sky-600);
    --color-background-accent-sky-hovered: var(--color-sky-500);
    --color-background-accent-sky-pressed: var(--color-sky-400);
    --color-background-accent-sky-alpha-default: var(--color-transparent);
    --color-background-accent-sky-alpha-hovered: var(--color-sky-200A);
    --color-background-accent-sky-alpha-pressed: var(--color-sky-300A);
    --color-background-accent-sky-bold-default: var(--color-sky-500);
    --color-background-accent-sky-bold-hovered: var(--color-sky-400);
    --color-background-accent-sky-bold-pressed: var(--color-sky-300);
    --color-background-accent-sky-bolder-default: var(--color-sky-500);
    --color-background-accent-sky-bolder-hovered: var(--color-sky-400);
    --color-background-accent-sky-bolder-pressed: var(--color-sky-300);
    --color-background-accent-sky-subtle-default: var(--color-sky-800);
    --color-background-accent-sky-subtle-hovered: var(--color-sky-700);
    --color-background-accent-sky-subtle-pressed: var(--color-sky-600);
    --color-background-accent-sky-subtlest-default: var(--color-sky-900);
    --color-background-accent-sky-subtlest-hovered: var(--color-sky-800);
    --color-background-accent-sky-subtlest-pressed: var(--color-sky-700);
    --color-background-accent-sky-white-default: var(--color-gray-800);
    --color-background-accent-sky-white-hovered: var(--color-sky-800);
    --color-background-accent-sky-white-pressed: var(--color-sky-700);
    --color-background-accent-teal-default: var(--color-teal-600);
    --color-background-accent-teal-hovered: var(--color-teal-500);
    --color-background-accent-teal-pressed: var(--color-teal-400);
    --color-background-accent-teal-alpha-default: var(--color-transparent);
    --color-background-accent-teal-alpha-hovered: var(--color-teal-200A);
    --color-background-accent-teal-alpha-pressed: var(--color-teal-300A);
    --color-background-accent-teal-bold-default: var(--color-teal-500);
    --color-background-accent-teal-bold-hovered: var(--color-teal-400);
    --color-background-accent-teal-bold-pressed: var(--color-teal-300);
    --color-background-accent-teal-bolder-default: var(--color-teal-500);
    --color-background-accent-teal-bolder-hovered: var(--color-teal-400);
    --color-background-accent-teal-bolder-pressed: var(--color-teal-300);
    --color-background-accent-teal-subtle-default: var(--color-teal-800);
    --color-background-accent-teal-subtle-hovered: var(--color-teal-700);
    --color-background-accent-teal-subtle-pressed: var(--color-teal-600);
    --color-background-accent-teal-subtlest-default: var(--color-teal-900);
    --color-background-accent-teal-subtlest-hovered: var(--color-teal-800);
    --color-background-accent-teal-subtlest-pressed: var(--color-teal-700);
    --color-background-accent-teal-white-default: var(--color-gray-800);
    --color-background-accent-teal-white-hovered: var(--color-teal-800);
    --color-background-accent-teal-white-pressed: var(--color-teal-700);
    --color-background-accent-yellow-default: var(--color-yellow-600);
    --color-background-accent-yellow-hovered: var(--color-yellow-500);
    --color-background-accent-yellow-pressed: var(--color-yellow-400);
    --color-background-accent-yellow-alpha-default: var(--color-transparent);
    --color-background-accent-yellow-alpha-hovered: var(--color-yellow-200A);
    --color-background-accent-yellow-alpha-pressed: var(--color-yellow-300A);
    --color-background-accent-yellow-bold-default: var(--color-yellow-500);
    --color-background-accent-yellow-bold-hovered: var(--color-yellow-400);
    --color-background-accent-yellow-bold-pressed: var(--color-yellow-300);
    --color-background-accent-yellow-bolder-default: var(--color-yellow-500);
    --color-background-accent-yellow-bolder-hovered: var(--color-yellow-400);
    --color-background-accent-yellow-bolder-pressed: var(--color-yellow-300);
    --color-background-accent-yellow-subtle-default: var(--color-yellow-800);
    --color-background-accent-yellow-subtle-hovered: var(--color-yellow-700);
    --color-background-accent-yellow-subtle-pressed: var(--color-yellow-600);
    --color-background-accent-yellow-subtlest-default: var(--color-yellow-900);
    --color-background-accent-yellow-subtlest-hovered: var(--color-yellow-800);
    --color-background-accent-yellow-subtlest-pressed: var(--color-yellow-700);
    --color-background-accent-yellow-white-default: var(--color-gray-800);
    --color-background-accent-yellow-white-hovered: var(--color-yellow-800);
    --color-background-accent-yellow-white-pressed: var(--color-yellow-700);
    --color-background-brand-default: var(--color-indigo-600);
    --color-background-brand-hovered: var(--color-indigo-500);
    --color-background-brand-pressed: var(--color-indigo-400);
    --color-background-brand-alpha-bold-default: var(--color-indigo-400A);
    --color-background-brand-alpha-bold-hovered: var(--color-indigo-500A);
    --color-background-brand-alpha-bold-pressed: var(--color-indigo-600);
    --color-background-brand-alpha-subtle-default: var(--color-transparent);
    --color-background-brand-alpha-subtle-hovered: var(--color-indigo-200A);
    --color-background-brand-alpha-subtle-pressed: var(--color-indigo-300A);
    --color-background-brand-bold-default: var(--color-indigo-500);
    --color-background-brand-bold-hovered: var(--color-indigo-400);
    --color-background-brand-bold-pressed: var(--color-indigo-300);
    --color-background-brand-bolder-default: var(--color-indigo-500);
    --color-background-brand-bolder-hovered: var(--color-indigo-400);
    --color-background-brand-bolder-pressed: var(--color-indigo-300);
    --color-background-brand-subtle-default: var(--color-indigo-800);
    --color-background-brand-subtle-hovered: var(--color-indigo-700);
    --color-background-brand-subtle-pressed: var(--color-indigo-600);
    --color-background-brand-subtlest-default: var(--color-indigo-900);
    --color-background-brand-subtlest-hovered: var(--color-indigo-800);
    --color-background-brand-subtlest-pressed: var(--color-indigo-700);
    --color-background-brand-white-default: var(--color-gray-800);
    --color-background-brand-white-hovered: var(--color-indigo-800);
    --color-background-brand-white-pressed: var(--color-indigo-700);
    --color-background-danger-default: var(--color-red-600);
    --color-background-danger-hovered: var(--color-red-500);
    --color-background-danger-pressed: var(--color-red-400);
    --color-background-danger-alpha-bold-default: var(--color-red-400A);
    --color-background-danger-alpha-bold-hovered: var(--color-red-500A);
    --color-background-danger-alpha-bold-pressed: var(--color-red-600);
    --color-background-danger-alpha-subtle-default: var(--color-transparent);
    --color-background-danger-alpha-subtle-hovered: var(--color-red-200A);
    --color-background-danger-alpha-subtle-pressed: var(--color-red-300A);
    --color-background-danger-bold-default: var(--color-red-500);
    --color-background-danger-bold-hovered: var(--color-red-400);
    --color-background-danger-bold-pressed: var(--color-red-300);
    --color-background-danger-bolder-default: var(--color-red-500);
    --color-background-danger-bolder-hovered: var(--color-red-400);
    --color-background-danger-bolder-pressed: var(--color-red-300);
    --color-background-danger-subtle-default: var(--color-red-800);
    --color-background-danger-subtle-hovered: var(--color-red-700);
    --color-background-danger-subtle-pressed: var(--color-red-600);
    --color-background-danger-subtlest-default: var(--color-red-900);
    --color-background-danger-subtlest-hovered: var(--color-red-800);
    --color-background-danger-subtlest-pressed: var(--color-red-700);
    --color-background-danger-white-default: var(--color-gray-800);
    --color-background-danger-white-hovered: var(--color-red-800);
    --color-background-danger-white-pressed: var(--color-red-700);
    --color-background-discovery-default: var(--color-purple-600);
    --color-background-discovery-hovered: var(--color-purple-500);
    --color-background-discovery-pressed: var(--color-purple-400);
    --color-background-discovery-alpha-bold-default: var(--color-purple-400A);
    --color-background-discovery-alpha-bold-hovered: var(--color-purple-500A);
    --color-background-discovery-alpha-bold-pressed: var(--color-purple-600);
    --color-background-discovery-alpha-subtle-default: var(--color-transparent);
    --color-background-discovery-alpha-subtle-hovered: var(--color-purple-200A);
    --color-background-discovery-alpha-subtle-pressed: var(--color-purple-300A);
    --color-background-discovery-bold-default: var(--color-purple-500);
    --color-background-discovery-bold-hovered: var(--color-purple-400);
    --color-background-discovery-bold-pressed: var(--color-purple-300);
    --color-background-discovery-bolder-default: var(--color-purple-500);
    --color-background-discovery-bolder-hovered: var(--color-purple-400);
    --color-background-discovery-bolder-pressed: var(--color-purple-300);
    --color-background-discovery-subtle-default: var(--color-purple-800);
    --color-background-discovery-subtle-hovered: var(--color-purple-700);
    --color-background-discovery-subtle-pressed: var(--color-purple-600);
    --color-background-discovery-subtlest-default: var(--color-purple-900);
    --color-background-discovery-subtlest-hovered: var(--color-purple-800);
    --color-background-discovery-subtlest-pressed: var(--color-purple-700);
    --color-background-discovery-white-default: var(--color-gray-800);
    --color-background-discovery-white-hovered: var(--color-purple-800);
    --color-background-discovery-white-pressed: var(--color-purple-700);
    --color-background-info-default: var(--color-blue-600);
    --color-background-info-hovered: var(--color-blue-700);
    --color-background-info-pressed: var(--color-blue-800);
    --color-background-info-alpha-bold-default: var(--color-blue-400A);
    --color-background-info-alpha-bold-hovered: var(--color-blue-500A);
    --color-background-info-alpha-bold-pressed: var(--color-blue-600);
    --color-background-info-alpha-subtle-default: var(--color-transparent);
    --color-background-info-alpha-subtle-hovered: var(--color-blue-200A);
    --color-background-info-alpha-subtle-pressed: var(--color-blue-300A);
    --color-background-info-bold-default: var(--color-blue-500);
    --color-background-info-bold-hovered: var(--color-blue-400);
    --color-background-info-bold-pressed: var(--color-blue-300);
    --color-background-info-bolder-default: var(--color-blue-500);
    --color-background-info-bolder-hovered: var(--color-blue-400);
    --color-background-info-bolder-pressed: var(--color-blue-300);
    --color-background-info-subtle-default: var(--color-blue-800);
    --color-background-info-subtle-hovered: var(--color-blue-700);
    --color-background-info-subtle-pressed: var(--color-blue-600);
    --color-background-info-subtlest-default: var(--color-blue-900);
    --color-background-info-subtlest-hovered: var(--color-blue-800);
    --color-background-info-subtlest-pressed: var(--color-blue-700);
    --color-background-info-white-default: var(--color-gray-800);
    --color-background-info-white-hovered: var(--color-blue-800);
    --color-background-info-white-pressed: var(--color-blue-700);
    --color-background-input-default: var(--color-gray-800);
    --color-background-input-disabled: var(--color-gray-700);
    --color-background-input-hovered: var(--color-gray-700);
    --color-background-input-pressed: var(--color-gray-800);
    --color-background-input-danger-default: var(--color-red-400);
    --color-background-input-danger-hovered: var(--color-red-300);
    --color-background-input-danger-pressed: var(--color-red-200);
    --color-background-input-selected-default: var(--color-indigo-400);
    --color-background-input-selected-hovered: var(--color-indigo-300);
    --color-background-input-selected-pressed: var(--color-indigo-200);
    --color-background-neutral-default: var(--color-gray-600);
    --color-background-neutral-hovered: var(--color-gray-500);
    --color-background-neutral-pressed: var(--color-gray-400);
    --color-background-neutral-alpha-bold-default: var(--color-white-400A);
    --color-background-neutral-alpha-bold-hovered: var(--color-white-500A);
    --color-background-neutral-alpha-bold-pressed: var(--color-white);
    --color-background-neutral-alpha-bold-inverted-default: var(--color-gray-400A);
    --color-background-neutral-alpha-bold-inverted-hovered: var(--color-gray-500A);
    --color-background-neutral-alpha-bold-inverted-pressed: var(--color-gray-600);
    --color-background-neutral-alpha-default-default: var(--color-white-200A);
    --color-background-neutral-alpha-default-hovered: var(--color-white-300A);
    --color-background-neutral-alpha-default-pressed: var(--color-white-400A);
    --color-background-neutral-alpha-default-inverted-default: var(--color-white-300A);
    --color-background-neutral-alpha-default-inverted-hovered: var(--color-white-400A);
    --color-background-neutral-alpha-default-inverted-pressed: var(--color-white-500A);
    --color-background-neutral-alpha-subltest-default: var(--color-transparent);
    --color-background-neutral-alpha-subltest-hovered: var(--color-white-100A);
    --color-background-neutral-alpha-subltest-pressed: var(--color-white-200A);
    --color-background-neutral-alpha-subltest-inverted-default: var(--color-transparent);
    --color-background-neutral-alpha-subltest-inverted-hovered: var(--color-gray-100A);
    --color-background-neutral-alpha-subltest-inverted-pressed: var(--color-gray-200A);
    --color-background-neutral-alpha-subtle-default: var(--color-white-100A);
    --color-background-neutral-alpha-subtle-hovered: var(--color-white-200A);
    --color-background-neutral-alpha-subtle-pressed: var(--color-white-300A);
    --color-background-neutral-alpha-subtle-inverted-default: var(--color-gray-100A);
    --color-background-neutral-alpha-subtle-inverted-hovered: var(--color-gray-200A);
    --color-background-neutral-alpha-subtle-inverted-pressed: var(--color-gray-300A);
    --color-background-neutral-bold-default: var(--color-gray-500);
    --color-background-neutral-bold-hovered: var(--color-gray-400);
    --color-background-neutral-bold-pressed: var(--color-gray-300);
    --color-background-neutral-bolder-default: var(--color-gray-500);
    --color-background-neutral-bolder-hovered: var(--color-gray-400);
    --color-background-neutral-bolder-pressed: var(--color-gray-300);
    --color-background-neutral-subtle-default: var(--color-gray-600);
    --color-background-neutral-subtle-hovered: var(--color-gray-500);
    --color-background-neutral-subtle-pressed: var(--color-gray-400);
    --color-background-neutral-subtlest-default: var(--color-gray-700);
    --color-background-neutral-subtlest-hovered: var(--color-gray-600);
    --color-background-neutral-subtlest-pressed: var(--color-gray-500);
    --color-background-neutral-white-default: var(--color-gray-800);
    --color-background-neutral-white-hovered: var(--color-gray-700);
    --color-background-neutral-white-pressed: var(--color-gray-600);
    --color-background-selected-default: var(--color-indigo-600);
    --color-background-selected-hovered: var(--color-indigo-500);
    --color-background-selected-pressed: var(--color-indigo-400);
    --color-background-selected-alpha-default: var(--color-transparent);
    --color-background-selected-alpha-hovered: var(--color-indigo-200A);
    --color-background-selected-alpha-pressed: var(--color-indigo-300A);
    --color-background-selected-bold-default: var(--color-indigo-500);
    --color-background-selected-bold-hovered: var(--color-indigo-400);
    --color-background-selected-bold-pressed: var(--color-indigo-300);
    --color-background-selected-bolder-default: var(--color-indigo-500);
    --color-background-selected-bolder-hovered: var(--color-indigo-400);
    --color-background-selected-bolder-pressed: var(--color-indigo-300);
    --color-background-selected-subtle-default: var(--color-indigo-800);
    --color-background-selected-subtle-hovered: var(--color-indigo-700);
    --color-background-selected-subtle-pressed: var(--color-indigo-600);
    --color-background-selected-white-default: var(--color-gray-800);
    --color-background-selected-white-hovered: var(--color-indigo-800);
    --color-background-selected-white-pressed: var(--color-indigo-700);
    --color-background-success-default: var(--color-emerald-600);
    --color-background-success-hovered: var(--color-emerald-500);
    --color-background-success-pressed: var(--color-emerald-400);
    --color-background-success-alpha-bold-default: var(--color-emerald-400A);
    --color-background-success-alpha-bold-hovered: var(--color-emerald-500A);
    --color-background-success-alpha-bold-pressed: var(--color-emerald-600);
    --color-background-success-alpha-subtle-default: var(--color-transparent);
    --color-background-success-alpha-subtle-hovered: var(--color-emerald-200A);
    --color-background-success-alpha-subtle-pressed: var(--color-emerald-300A);
    --color-background-success-bold-default: var(--color-emerald-500);
    --color-background-success-bold-hovered: var(--color-emerald-400);
    --color-background-success-bold-pressed: var(--color-emerald-300);
    --color-background-success-bolder-default: var(--color-emerald-500);
    --color-background-success-bolder-hovered: var(--color-emerald-400);
    --color-background-success-bolder-pressed: var(--color-emerald-300);
    --color-background-success-subtle-default: var(--color-emerald-800);
    --color-background-success-subtle-hovered: var(--color-emerald-700);
    --color-background-success-subtle-pressed: var(--color-emerald-600);
    --color-background-success-subtlest-default: var(--color-emerald-900);
    --color-background-success-subtlest-hovered: var(--color-emerald-800);
    --color-background-success-subtlest-pressed: var(--color-emerald-700);
    --color-background-success-white-default: var(--color-gray-800);
    --color-background-success-white-hovered: var(--color-emerald-800);
    --color-background-success-white-pressed: var(--color-emerald-700);
    --color-background-warning-default: var(--color-yellow-600);
    --color-background-warning-hovered: var(--color-yellow-500);
    --color-background-warning-pressed: var(--color-yellow-400);
    --color-background-warning-alpha-bold-default: var(--color-yellow-400A);
    --color-background-warning-alpha-bold-hovered: var(--color-yellow-500A);
    --color-background-warning-alpha-bold-pressed: var(--color-yellow-600);
    --color-background-warning-alpha-subtle-default: var(--color-transparent);
    --color-background-warning-alpha-subtle-hovered: var(--color-yellow-200A);
    --color-background-warning-alpha-subtle-pressed: var(--color-yellow-300A);
    --color-background-warning-bold-default: var(--color-yellow-500);
    --color-background-warning-bold-hovered: var(--color-yellow-400);
    --color-background-warning-bold-pressed: var(--color-yellow-300);
    --color-background-warning-bolder-default: var(--color-yellow-500);
    --color-background-warning-bolder-hovered: var(--color-yellow-400);
    --color-background-warning-bolder-pressed: var(--color-yellow-300);
    --color-background-warning-subtle-default: var(--color-yellow-800);
    --color-background-warning-subtle-hovered: var(--color-yellow-700);
    --color-background-warning-subtle-pressed: var(--color-yellow-600);
    --color-background-warning-subtlest-default: var(--color-yellow-900);
    --color-background-warning-subtlest-hovered: var(--color-yellow-800);
    --color-background-warning-subtlest-pressed: var(--color-yellow-700);
    --color-background-warning-white-default: var(--color-gray-800);
    --color-background-warning-white-hovered: var(--color-yellow-800);
    --color-background-warning-white-pressed: var(--color-yellow-700);
    --color-border-accent-blue-bold: var(--color-blue-400);
    --color-border-accent-blue-default: var(--color-blue-600);
    --color-border-accent-blue-subtle: var(--color-blue-700);
    --color-border-accent-blue-subtlest: var(--color-blue-800);
    --color-border-accent-cyan-bold: var(--color-cyan-400);
    --color-border-accent-cyan-default: var(--color-cyan-600);
    --color-border-accent-cyan-subtle: var(--color-cyan-700);
    --color-border-accent-cyan-subtlest: var(--color-cyan-800);
    --color-border-accent-emerald-bold: var(--color-emerald-400);
    --color-border-accent-emerald-default: var(--color-emerald-600);
    --color-border-accent-emerald-subtle: var(--color-emerald-700);
    --color-border-accent-emerald-subtlest: var(--color-emerald-800);
    --color-border-accent-fuchsia-bold: var(--color-fuchsia-400);
    --color-border-accent-fuchsia-default: var(--color-fuchsia-600);
    --color-border-accent-fuchsia-subtle: var(--color-fuchsia-700);
    --color-border-accent-fuchsia-subtlest: var(--color-fuchsia-800);
    --color-border-accent-green-bold: var(--color-green-400);
    --color-border-accent-green-default: var(--color-green-600);
    --color-border-accent-green-subtle: var(--color-green-700);
    --color-border-accent-green-subtlest: var(--color-green-800);
    --color-border-accent-indigo-bold: var(--color-indigo-400);
    --color-border-accent-indigo-default: var(--color-indigo-600);
    --color-border-accent-indigo-subtle: var(--color-indigo-700);
    --color-border-accent-indigo-subtlest: var(--color-indigo-800);
    --color-border-accent-lime-bold: var(--color-lime-400);
    --color-border-accent-lime-default: var(--color-lime-600);
    --color-border-accent-lime-subtle: var(--color-lime-700);
    --color-border-accent-lime-subtlest: var(--color-lime-800);
    --color-border-accent-orange-bold: var(--color-orange-400);
    --color-border-accent-orange-default: var(--color-orange-600);
    --color-border-accent-orange-subtle: var(--color-orange-700);
    --color-border-accent-orange-subtlest: var(--color-orange-800);
    --color-border-accent-pink-bold: var(--color-pink-400);
    --color-border-accent-pink-default: var(--color-pink-600);
    --color-border-accent-pink-subtle: var(--color-pink-700);
    --color-border-accent-pink-subtlest: var(--color-pink-800);
    --color-border-accent-purple-bold: var(--color-purple-400);
    --color-border-accent-purple-default: var(--color-purple-600);
    --color-border-accent-purple-subtle: var(--color-purple-700);
    --color-border-accent-purple-subtlest: var(--color-purple-800);
    --color-border-accent-red-bold: var(--color-red-400);
    --color-border-accent-red-default: var(--color-red-600);
    --color-border-accent-red-subtle: var(--color-red-700);
    --color-border-accent-red-subtlest: var(--color-red-800);
    --color-border-accent-rose-bold: var(--color-rose-400);
    --color-border-accent-rose-default: var(--color-rose-600);
    --color-border-accent-rose-subtle: var(--color-rose-700);
    --color-border-accent-rose-subtlest: var(--color-rose-800);
    --color-border-accent-rosedust-bold: var(--color-rosedust-400);
    --color-border-accent-rosedust-default: var(--color-rosedust-600);
    --color-border-accent-rosedust-subtle: var(--color-rosedust-700);
    --color-border-accent-rosedust-subtlest: var(--color-rosedust-800);
    --color-border-accent-sky-bold: var(--color-sky-400);
    --color-border-accent-sky-default: var(--color-sky-600);
    --color-border-accent-sky-subtle: var(--color-sky-700);
    --color-border-accent-sky-subtlest: var(--color-sky-800);
    --color-border-accent-teal-bold: var(--color-teal-400);
    --color-border-accent-teal-default: var(--color-teal-600);
    --color-border-accent-teal-subtle: var(--color-teal-700);
    --color-border-accent-teal-subtlest: var(--color-teal-800);
    --color-border-accent-yellow-bold: var(--color-yellow-400);
    --color-border-accent-yellow-default: var(--color-yellow-600);
    --color-border-accent-yellow-subtle: var(--color-yellow-700);
    --color-border-accent-yellow-subtlest: var(--color-yellow-800);
    --color-border-brand-bold: var(--color-indigo-400);
    --color-border-brand-default: var(--color-indigo-600);
    --color-border-brand-subtle: var(--color-indigo-700);
    --color-border-brand-subtlest: var(--color-indigo-800);
    --color-border-danger-bold: var(--color-red-400);
    --color-border-danger-default: var(--color-red-600);
    --color-border-danger-subtle: var(--color-red-700);
    --color-border-danger-subtlest: var(--color-red-800);
    --color-border-discovery-bold: var(--color-purple-400);
    --color-border-discovery-default: var(--color-purple-600);
    --color-border-discovery-subtle: var(--color-purple-700);
    --color-border-discovery-subtlest: var(--color-purple-800);
    --color-border-info-bold: var(--color-blue-400);
    --color-border-info-default: var(--color-blue-600);
    --color-border-info-subtle: var(--color-blue-700);
    --color-border-info-subtlest: var(--color-blue-800);
    --color-border-input-danger: var(--color-red-500);
    --color-border-input-default: var(--color-white-300A);
    --color-border-input-disabled: var(--color-white-200A);
    --color-border-input-hovered: var(--color-white-400A);
    --color-border-input-selected: var(--color-indigo-400);
    --color-border-neutral-default: var(--color-gray-500);
    --color-border-neutral-disabled: var(--color-gray-700);
    --color-border-neutral-hovered: var(--color-gray-400);
    --color-border-neutral-inverse: var(--color-gray-800);
    --color-border-neutral-bold-default: var(--color-gray-300);
    --color-border-neutral-bold-hovered: var(--color-gray-200);
    --color-border-neutral-subtle-default: var(--color-gray-600);
    --color-border-neutral-subtle-hovered: var(--color-gray-500);
    --color-border-neutral-subtlest-default: var(--color-gray-700);
    --color-border-neutral-subtlest-hovered: var(--color-gray-600);
    --color-border-selected-default: var(--color-indigo-400);
    --color-border-selected-hover: var(--color-indigo-300);
    --color-border-selected-subtle-default: var(--color-indigo-600);
    --color-border-selected-subtle-hover: var(--color-indigo-500);
    --color-border-selected-subtlest-default: var(--color-indigo-700);
    --color-border-selected-subtlest-hover: var(--color-indigo-600);
    --color-border-success-bold: var(--color-emerald-400);
    --color-border-success-default: var(--color-emerald-600);
    --color-border-success-subtle: var(--color-emerald-700);
    --color-border-success-subtlest: var(--color-emerald-800);
    --color-border-warning-bold: var(--color-yellow-400);
    --color-border-warning-default: var(--color-yellow-600);
    --color-border-warning-subtle: var(--color-yellow-700);
    --color-border-warning-subtlest: var(--color-yellow-800);
    --color-icon-accent-blue-bold: var(--color-blue-300);
    --color-icon-accent-blue-default: var(--color-blue-400);
    --color-icon-accent-blue-inverse: var(--color-white);
    --color-icon-accent-blue-subtle: var(--color-blue-500);
    --color-icon-accent-blue-subtlest: var(--color-blue-600);
    --color-icon-accent-cyan-bold: var(--color-cyan-300);
    --color-icon-accent-cyan-default: var(--color-cyan-400);
    --color-icon-accent-cyan-inverse: var(--color-white);
    --color-icon-accent-cyan-subtle: var(--color-cyan-500);
    --color-icon-accent-cyan-subtlest: var(--color-cyan-600);
    --color-icon-accent-emerald-bold: var(--color-emerald-300);
    --color-icon-accent-emerald-default: var(--color-emerald-400);
    --color-icon-accent-emerald-inverse: var(--color-white);
    --color-icon-accent-emerald-subtle: var(--color-emerald-500);
    --color-icon-accent-emerald-subtlest: var(--color-emerald-600);
    --color-icon-accent-fuchsia-bold: var(--color-fuchsia-300);
    --color-icon-accent-fuchsia-default: var(--color-fuchsia-400);
    --color-icon-accent-fuchsia-inverse: var(--color-white);
    --color-icon-accent-fuchsia-subtle: var(--color-fuchsia-500);
    --color-icon-accent-fuchsia-subtlest: var(--color-fuchsia-600);
    --color-icon-accent-green-bold: var(--color-green-300);
    --color-icon-accent-green-default: var(--color-green-400);
    --color-icon-accent-green-inverse: var(--color-white);
    --color-icon-accent-green-subtle: var(--color-green-500);
    --color-icon-accent-green-subtlest: var(--color-green-600);
    --color-icon-accent-indigo-bold: var(--color-indigo-300);
    --color-icon-accent-indigo-default: var(--color-indigo-400);
    --color-icon-accent-indigo-inverse: var(--color-white);
    --color-icon-accent-indigo-subtle: var(--color-indigo-500);
    --color-icon-accent-indigo-subtlest: var(--color-indigo-600);
    --color-icon-accent-lime-bold: var(--color-lime-300);
    --color-icon-accent-lime-default: var(--color-lime-400);
    --color-icon-accent-lime-inverse: var(--color-white);
    --color-icon-accent-lime-subtle: var(--color-lime-500);
    --color-icon-accent-lime-subtlest: var(--color-lime-600);
    --color-icon-accent-orange-bold: var(--color-orange-300);
    --color-icon-accent-orange-default: var(--color-orange-400);
    --color-icon-accent-orange-inverse: var(--color-white);
    --color-icon-accent-orange-subtle: var(--color-orange-500);
    --color-icon-accent-orange-subtlest: var(--color-orange-600);
    --color-icon-accent-pink-bold: var(--color-pink-300);
    --color-icon-accent-pink-default: var(--color-pink-400);
    --color-icon-accent-pink-inverse: var(--color-white);
    --color-icon-accent-pink-subtle: var(--color-pink-500);
    --color-icon-accent-pink-subtlest: var(--color-pink-600);
    --color-icon-accent-purple-bold: var(--color-purple-300);
    --color-icon-accent-purple-default: var(--color-purple-400);
    --color-icon-accent-purple-inverse: var(--color-white);
    --color-icon-accent-purple-subtle: var(--color-purple-500);
    --color-icon-accent-purple-subtlest: var(--color-purple-600);
    --color-icon-accent-red-bold: var(--color-red-300);
    --color-icon-accent-red-default: var(--color-red-400);
    --color-icon-accent-red-inverse: var(--color-white);
    --color-icon-accent-red-subtle: var(--color-red-500);
    --color-icon-accent-red-subtlest: var(--color-red-600);
    --color-icon-accent-rose-bold: var(--color-rose-300);
    --color-icon-accent-rose-default: var(--color-rose-400);
    --color-icon-accent-rose-inverse: var(--color-white);
    --color-icon-accent-rose-subtle: var(--color-rose-500);
    --color-icon-accent-rose-subtlest: var(--color-rose-600);
    --color-icon-accent-rosedust-bold: var(--color-rosedust-300);
    --color-icon-accent-rosedust-default: var(--color-rosedust-400);
    --color-icon-accent-rosedust-inverse: var(--color-white);
    --color-icon-accent-rosedust-subtle: var(--color-rosedust-500);
    --color-icon-accent-rosedust-subtlest: var(--color-rosedust-600);
    --color-icon-accent-sky-bold: var(--color-sky-300);
    --color-icon-accent-sky-default: var(--color-sky-400);
    --color-icon-accent-sky-inverse: var(--color-white);
    --color-icon-accent-sky-subtle: var(--color-sky-500);
    --color-icon-accent-sky-subtlest: var(--color-sky-600);
    --color-icon-accent-teal-bold: var(--color-teal-300);
    --color-icon-accent-teal-default: var(--color-teal-400);
    --color-icon-accent-teal-inverse: var(--color-white);
    --color-icon-accent-teal-subtle: var(--color-teal-500);
    --color-icon-accent-teal-subtlest: var(--color-teal-600);
    --color-icon-accent-yellow-bold: var(--color-yellow-300);
    --color-icon-accent-yellow-default: var(--color-yellow-400);
    --color-icon-accent-yellow-inverse: var(--color-white);
    --color-icon-accent-yellow-subtle: var(--color-yellow-500);
    --color-icon-accent-yellow-subtlest: var(--color-yellow-600);
    --color-icon-brand-bold: var(--color-indigo-300);
    --color-icon-brand-default: var(--color-indigo-400);
    --color-icon-brand-inverse: var(--color-white);
    --color-icon-brand-subtle: var(--color-indigo-500);
    --color-icon-brand-subtlest: var(--color-indigo-600);
    --color-icon-danger-bold: var(--color-red-300);
    --color-icon-danger-default: var(--color-red-400);
    --color-icon-danger-inverse: var(--color-white);
    --color-icon-danger-subtle: var(--color-red-500);
    --color-icon-danger-subtlest: var(--color-red-600);
    --color-icon-discovery-bold: var(--color-purple-300);
    --color-icon-discovery-default: var(--color-purple-400);
    --color-icon-discovery-inverse: var(--color-white);
    --color-icon-discovery-subtle: var(--color-purple-500);
    --color-icon-discovery-subtlest: var(--color-purple-600);
    --color-icon-info-bold: var(--color-blue-300);
    --color-icon-info-default: var(--color-blue-400);
    --color-icon-info-inverse: var(--color-white);
    --color-icon-info-subtle: var(--color-blue-500);
    --color-icon-info-subtlest: var(--color-blue-600);
    --color-icon-neutral-bold: var(--color-gray-300);
    --color-icon-neutral-default: var(--color-gray-400);
    --color-icon-neutral-inverse: var(--color-white);
    --color-icon-neutral-subtle: var(--color-gray-500);
    --color-icon-neutral-subtlest: var(--color-gray-600);
    --color-icon-selected-bold: var(--color-indigo-300);
    --color-icon-selected-default: var(--color-indigo-400);
    --color-icon-selected-inverse: var(--color-white);
    --color-icon-selected-subtle: var(--color-indigo-500);
    --color-icon-selected-subtlest: var(--color-indigo-600);
    --color-icon-success-bold: var(--color-emerald-300);
    --color-icon-success-default: var(--color-emerald-400);
    --color-icon-success-inverse: var(--color-white);
    --color-icon-success-subtle: var(--color-emerald-500);
    --color-icon-success-subtlest: var(--color-emerald-600);
    --color-icon-warning-bold: var(--color-yellow-300);
    --color-icon-warning-default: var(--color-yellow-400);
    --color-icon-warning-inverse: var(--color-white);
    --color-icon-warning-subtle: var(--color-yellow-500);
    --color-icon-warning-subtlest: var(--color-yellow-600);
    --color-interaction-hovered: var(--color-white-200A);
    --color-interaction-pressed: var(--color-white-300A);
    --color-link-brand-default: var(--color-indigo-400);
    --color-link-brand-hovered: var(--color-indigo-200);
    --color-link-brand-pressed: var(--color-indigo-500);
    --color-link-danger-default: var(--color-red-500);
    --color-link-danger-hovered: var(--color-red-300);
    --color-link-danger-pressed: var(--color-red-600);
    --color-link-discovery-default: var(--color-purple-500);
    --color-link-discovery-hovered: var(--color-purple-300);
    --color-link-discovery-pressed: var(--color-purple-600);
    --color-link-info-default: var(--color-blue-500);
    --color-link-info-hovered: var(--color-blue-300);
    --color-link-info-pressed: var(--color-blue-600);
    --color-link-inverse-default: var(--color-indigo-800);
    --color-link-inverse-hovered: var(--color-indigo-600);
    --color-link-inverse-pressed: var(--color-indigo-700);
    --color-link-neutral-default: var(--color-gray-500);
    --color-link-neutral-hovered: var(--color-gray-300);
    --color-link-neutral-pressed: var(--color-gray-600);
    --color-link-success-default: var(--color-emerald-500);
    --color-link-success-hovered: var(--color-emerald-300);
    --color-link-success-pressed: var(--color-emerald-600);
    --color-link-warning-default: var(--color-yellow-500);
    --color-link-warning-hovered: var(--color-yellow-300);
    --color-link-warning-pressed: var(--color-yellow-600);
    --color-overlay-default: var(--color-gray-600A);
    --color-overlay-subtle: var(--color-gray-400A);
    --color-overlay-inverse-default: var(--color-white-500A);
    --color-overlay-inverse-subtle: var(--color-white-400A);
    --color-skeleton-default: var(--color-white-200A);
    --color-skeleton-subtle: var(--color-white-100A);
    --color-surface-default: var(--color-gray-900);
    --color-surface-hovered: var(--color-gray-800);
    --color-surface-pressed: var(--color-gray-700);
    --color-surface-inverse-default: var(--color-gray-800);
    --color-surface-inverse-hovered: var(--color-gray-700);
    --color-surface-inverse-pressed: var(--color-gray-600);
    --color-surface-navigation-default: var(--color-gray-800);
    --color-surface-navigation-hovered: var(--color-gray-700);
    --color-surface-navigation-pressed: var(--color-gray-600);
    --color-surface-overlay-default: var(--color-gray-800);
    --color-surface-overlay-hovered: var(--color-gray-700);
    --color-surface-overlay-pressed: var(--color-gray-600);
    --color-surface-raised-default: var(--color-gray-800);
    --color-surface-raised-hovered: var(--color-gray-700);
    --color-surface-raised-pressed: var(--color-gray-600);
    --color-surface-sunken-default: var(--color-gray-900);
    --color-text-accent-blue-default: var(--color-blue-300);
    --color-text-accent-blue-inverse: var(--color-white);
    --color-text-accent-blue-subtle: var(--color-blue-400);
    --color-text-accent-cyan-default: var(--color-cyan-300);
    --color-text-accent-cyan-inverse: var(--color-white);
    --color-text-accent-cyan-subtle: var(--color-cyan-400);
    --color-text-accent-emerald-default: var(--color-emerald-300);
    --color-text-accent-emerald-inverse: var(--color-white);
    --color-text-accent-emerald-subtle: var(--color-emerald-400);
    --color-text-accent-fuchsia-default: var(--color-fuchsia-300);
    --color-text-accent-fuchsia-inverse: var(--color-white);
    --color-text-accent-fuchsia-subtle: var(--color-fuchsia-400);
    --color-text-accent-green-default: var(--color-green-300);
    --color-text-accent-green-inverse: var(--color-white);
    --color-text-accent-green-subtle: var(--color-green-400);
    --color-text-accent-indigo-default: var(--color-indigo-300);
    --color-text-accent-indigo-inverse: var(--color-white);
    --color-text-accent-indigo-subtle: var(--color-indigo-400);
    --color-text-accent-lime-default: var(--color-lime-300);
    --color-text-accent-lime-inverse: var(--color-white);
    --color-text-accent-lime-subtle: var(--color-lime-400);
    --color-text-accent-orange-default: var(--color-orange-300);
    --color-text-accent-orange-inverse: var(--color-white);
    --color-text-accent-orange-subtle: var(--color-orange-400);
    --color-text-accent-pink-default: var(--color-pink-300);
    --color-text-accent-pink-inverse: var(--color-white);
    --color-text-accent-pink-subtle: var(--color-pink-400);
    --color-text-accent-purple-default: var(--color-purple-300);
    --color-text-accent-purple-inverse: var(--color-white);
    --color-text-accent-purple-subtle: var(--color-purple-400);
    --color-text-accent-red-default: var(--color-red-300);
    --color-text-accent-red-inverse: var(--color-white);
    --color-text-accent-red-subtle: var(--color-red-400);
    --color-text-accent-rose-default: var(--color-rose-300);
    --color-text-accent-rose-inverse: var(--color-white);
    --color-text-accent-rose-subtle: var(--color-rose-400);
    --color-text-accent-rosedust-default: var(--color-rosedust-300);
    --color-text-accent-rosedust-inverse: var(--color-white);
    --color-text-accent-rosedust-subtle: var(--color-rosedust-400);
    --color-text-accent-sky-default: var(--color-sky-300);
    --color-text-accent-sky-inverse: var(--color-white);
    --color-text-accent-sky-subtle: var(--color-sky-400);
    --color-text-accent-teal-default: var(--color-teal-300);
    --color-text-accent-teal-inverse: var(--color-white);
    --color-text-accent-teal-subtle: var(--color-teal-400);
    --color-text-accent-yellow-default: var(--color-yellow-300);
    --color-text-accent-yellow-inverse: var(--color-white);
    --color-text-accent-yellow-subtle: var(--color-yellow-400);
    --color-text-brand-bold: var(--color-indigo-200);
    --color-text-brand-default: var(--color-indigo-300);
    --color-text-brand-inverse: var(--color-white);
    --color-text-brand-subtle: var(--color-indigo-400);
    --color-text-brand-subtlest: var(--color-indigo-500);
    --color-text-danger-default: var(--color-red-300);
    --color-text-danger-inverse: var(--color-white);
    --color-text-danger-subtle: var(--color-red-400);
    --color-text-discovery-default: var(--color-purple-300);
    --color-text-discovery-inverse: var(--color-white);
    --color-text-discovery-subtle: var(--color-purple-400);
    --color-text-info-default: var(--color-blue-300);
    --color-text-info-inverse: var(--color-white);
    --color-text-info-subtle: var(--color-blue-400);
    --color-text-neutral-bold: var(--color-gray-50);
    --color-text-neutral-default: var(--color-gray-100);
    --color-text-neutral-disabled: var(--color-gray-600);
    --color-text-neutral-inverse: var(--color-white);
    --color-text-neutral-subtle: var(--color-gray-400);
    --color-text-neutral-subtlest: var(--color-gray-500);
    --color-text-selected-bold: var(--color-indigo-200);
    --color-text-selected-default: var(--color-indigo-300);
    --color-text-selected-inverse: var(--color-white);
    --color-text-selected-subtle: var(--color-indigo-400);
    --color-text-selected-subtlest: var(--color-indigo-500);
    --color-text-success-default: var(--color-emerald-300);
    --color-text-success-inverse: var(--color-white);
    --color-text-success-subtle: var(--color-emerald-400);
    --color-text-warning-default: var(--color-yellow-300);
    --color-text-warning-inverse: var(--color-white);
    --color-text-warning-subtle: var(--color-yellow-400);
  }
}
